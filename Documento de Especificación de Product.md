Documento de Especificación de Producto: Salonier (Visión Completa)
Versión: 4.0 (Definitiva - Visión Completa)
 Fecha: 4 de junio de 2025
 Proyecto: Salonier
1. Introducción
1.1. Propósito de Salonier
Salonier se concibe como una plataforma tecnológica integral y el ecosistema digital definitivo para el profesional de la coloración capilar y la gestión de salones de belleza. Su propósito es fusionar la profesión de la peluquería con la ciencia y la inteligencia artificial, empoderando a los estilistas para que alcancen nuevos niveles de precisión, eficiencia, creatividad y satisfacción del cliente, siempre dentro de un marco de responsabilidad, cumplimiento normativo, optimización de recursos y excelencia en la experiencia de usuario.
1.2. Visión General de la Aplicación
Salonier será una solución multiplataforma (con una aplicación móvil nativa para ios, android y versión web como principal punto de interacción para el estilista) que ofrece un asistente de coloración inteligente, gestión integral del salón y herramientas de interacción con el cliente. Busca ser el estándar de oro en tecnología para la industria de la belleza, operando con los más altos estándares de privacidad, seguridad, eficiencia y usabilidad.
2. Objetivos del Producto (Visión a Largo Plazo)
Establecer un nuevo estándar de excelencia y precisión en los servicios de coloración capilar a nivel global.
Maximizar la rentabilidad y eficiencia operativa de los salones de belleza a través de la automatización inteligente, la optimización de recursos (incluyendo costes de tecnología) y la toma de decisiones basada en datos.
Fomentar una relación más profunda y personalizada entre el estilista y sus clientes, basada en la confianza, la transparencia en el manejo de datos y una experiencia de usuario fluida y agradable.
Crear una comunidad de profesionales de la belleza que comparten conocimientos y elevan la industria.
Convertirse en la herramienta indispensable en la que confían los estilistas para cada aspecto de su trabajo de coloración y gestión.
3. Usuarios Objetivo
Estilistas de Coloración Profesionales: Independientes, empleados en salones de todos los tamaños.
Dueños y Gerentes de Salones de Belleza: Buscando herramientas para optimizar la gestión, mejorar la rentabilidad, estandarizar la calidad, gestionar personal y analizar el rendimiento del negocio.
Asistentes y Recepcionistas de Salón: Utilizando módulos de agenda, comunicación con clientes y posiblemente facturación básica.
(Consideración Futura) Educadores y Academias de Peluquería: Utilizando la plataforma como herramienta de enseñanza y seguimiento de progreso para estudiantes.
(Futuro) Clientes Finales del Salón: Accediendo a su historial, información de citas, y recomendaciones personalizadas a través de un portal o app complementaria.
4. Funcionalidades Principales (Visión Completa de Salonier)
4.1. Autenticación Segura, Roles, Permisos y Personalización del Perfil
Registro y login con email/contraseña.
Inicio de sesión social (Google, Apple) para mayor comodidad y seguridad.
Autenticación de dos factores (2FA) opcional para una capa extra de seguridad.
Perfil de estilista detallado y editable, permitiendo gestionar información personal (nombre completo, teléfono), foto de perfil, información profesional (dirección del salón/trabajo, biografía/experiencia), especialidades y certificaciones, como se ha visualizado en las interfaces de "Editar Perfil".
Configuración de preferencias de la aplicación: marcas y líneas de productos de coloración y cuidado capilar favoritas/habituales, unidades de medida preferidas, configuración de notificaciones y alertas, e idioma de la interfaz.
Gestión de Roles y Permisos (Multi-usuario en Salón):
Roles Predefinidos: Administrador de Salón (dueño/gerente), Estilista, Recepcionista.
Permisos Granulares: El Administrador de Salón podrá configurar qué módulos y datos puede ver o editar cada rol (ej. recepcionista puede gestionar citas pero no ver analíticas financieras detalladas; estilistas ven sus clientes y citas, pero el admin ve todo el salón).
Esto permite una estructura jerárquica y segura para salones con múltiples empleados.
Consideración UX: Proceso de registro y login ágil y sin fricciones. Recuperación de contraseña intuitiva. Interfaz clara para la gestión de roles por parte del administrador.
4.2. Flujo de Consulta de Coloración Ultra-Inteligente Asistido por IA
4.2.1. Selección de Cliente para la Consulta
Búsqueda y selección fácil del cliente existente o opción para añadir uno nuevo, como se ha visualizado en las interfaces de usuario diseñadas para la selección de clientes.
Consentimiento Informado: Antes de iniciar la consulta y la captura de imágenes, la aplicación deberá recordar al estilista la importancia de obtener el consentimiento explícito del cliente para tomar fotografías y utilizar sus datos (incluyendo imágenes) con fines de análisis y documentación del servicio. Se podría ofrecer una plantilla de consentimiento simple o un checklist.
Consideración UX: Búsqueda rápida y eficiente de clientes, con opción de "cliente invitado" para consultas rápidas sin crear perfil completo inicialmente.
4.2.2. Diagnóstico Capilar Exhaustivo (Fase 1)
Captura Multi-Imagen Avanzada y Procesamiento para Privacidad:
Guías interactivas en la app móvil para la toma óptima de imágenes (buena iluminación, sin filtros, diferentes ángulos).
Permitir la subida de múltiples imágenes (ej. 3-7) para un análisis 360°, con opción de etiquetado por zona (raíces, medios, puntas; frontal, coronilla, laterales, nuca), similar a lo conceptualizado en las interfaces detalladas de diagnóstico, como la que muestra campos por zonas y opciones para cargar imágenes.
Consideraciones de UX y Costes para Imágenes:
Número de Imágenes: Para un diagnóstico inicial, guiar al estilista a tomar un mínimo de 3 imágenes clave (ej. frontal, coronilla, nuca) y permitir hasta un máximo razonable (ej. 5-7) para casos complejos.
Calidad sobre Cantidad: Enfatizar en la guía la importancia de fotos bien iluminadas y enfocadas.
Feedback Inmediato (Opcional): Explorar análisis en dispositivo (con Tensorflow.js si es viable) para indicar calidad de imagen antes de la subida.
Pre-procesamiento de Imágenes para Cumplimiento con IA y Privacidad:
Detección y Difuminado de Rostros: Implementar sistema para detectar y difuminar/pixelar rostros antes de enviar a APIs de IA, para cumplir políticas y proteger privacidad.
Implementación Técnica (Opciones): En cliente (usando expo-face-detector y expo-image-manipulator o Tensorflow.js) o en backend (Supabase Edge Function).
Optimización de Costes: Realizar difuminado y compresión en cliente.
Compresión inteligente de imágenes en el cliente (después del difuminado si se hace en cliente).
Análisis Profundo por IA (GPT-4 Vision y modelos especializados): (Sobre imágenes con rostros difuminados)
Identificación automática de: nivel natural, subtono, porcentaje de canas, diámetro, densidad, porosidad, elasticidad, resistencia, reflejos existentes.
Evaluación del estado de salud general del cabello.
Optimización de Costes IA:
Prompts Específicos: Diseñar prompts dirigidos, solicitando solo información necesaria.
Elección de Modelo IA: Evaluar modelos más económicos para sub-análisis.
Historial Químico del Cliente:
Registro y consideración automática por la IA de tratamientos químicos previos documentados, tal como se sugiere en las interfaces de diagnóstico que incluyen un apartado para historial de tratamientos.
Validación y Enriquecimiento por el Estilista:
La IA pre-rellenará campos del diagnóstico.
El estilista valida, edita, añade observaciones y confirma, manteniendo control final, como se visualiza en las interfaces de diagnóstico detalladas.
Consideración UX: Interfaz clara (origen datos IA vs. Estilista). Edición rápida (selectores, sliders).
4.2.3. Definición Precisa del Color Deseado (Fase 2)
Subida de imágenes de referencia (aplican consideraciones de difuminado).
Herramientas de selección: Análisis IA de imágenes (tono, reflejos, matices, luminosidad) y/o paletas digitales.
Posibilidad de descripción verbal del objetivo por el estilista.
Validación y ajuste fino por el estilista.
Consideraciones de UX y Costes para Imágenes de Referencia: Limitar a 1-3 imágenes bien elegidas.
4.2.4. Formulación Experta y Segura Asistida por IA (Fase 3)
Entradas Clave para la IA: Diagnóstico, color deseado, historial químico, marcas/líneas preferidas, y (opcionalmente) productos en stock.
Generación de Fórmula Detallada y Plan de Acción: Sugerencia de productos exactos, proporciones, volúmenes, tiempos de pose, e instrucciones paso a paso.
Análisis de Viabilidad, Riesgo y Proceso por IA: Evaluación de salud capilar, alcanzabilidad (sesiones), alertas de incompatibilidades, sugerencias de tratamientos pre/post.
Control Total del Estilista: IA es un consultor; estilista revisa, modifica o crea su propia formulación.
Consideraciones UX y de Costes para Formulación IA:
Transparencia de la IA: Comunicación clara si no hay alta confianza, ofreciendo alternativas.
Iteración con la IA: Permitir refinar la solicitud a la IA.
Guardado de Preferencias de Formulación: Aprendizaje de modificaciones (visión a largo plazo).
4.2.5. Documentación Exhaustiva del Servicio (Fase 4)
Subida de fotos del resultado final (con recordatorio/herramienta de difuminado).
Registro de la fórmula final aplicada.
Notas detalladas del estilista y comentarios del cliente.
Valoración opcional de satisfacción del cliente.
Vinculación al historial del cliente y a la cita.
4.3. Gestión Integral de Clientes
Perfiles de cliente 360°: Datos de contacto, historial completo (fechas, diagnósticos, fórmulas, fotos, productos, notas, coste), tests de alergia, preferencias, cumpleaños, como se visualiza en las interfaces de gestión de clientes y en los menús de "Gestión Avanzada".
Galería de inspiración personalizada por cliente.
Segmentación Avanzada de Clientes: Creación de segmentos personalizados (frecuencia, gasto, servicios, inactividad, etc.), con visualización de distribución, tal como se ha diseñado en las pantallas de segmentación que muestran filtros y gráficos.
Herramientas de Comunicación y Marketing con Clientes: Envío de recordatorios de citas (SMS, Email, Push), plantillas personalizables con variables, campañas segmentadas (promociones, novedades), gestión de consentimiento, como se muestra en las interfaces de comunicación con selección de tipo de mensaje y clientes.
Consideración de Seguridad y Privacidad: Cumplimiento de normativas anti-spam, consentimiento explícito para marketing.
Consideración UX: Acceso rápido a acciones comunes desde el perfil del cliente.
4.4. Gestión de Citas y Agenda Inteligente
Calendario visual e interactivo (vistas diaria, semanal, mensual, por estilista), programación rápida (cliente, servicio(s), fecha, hora, duración), cálculo automático de duración, gestión de tiempos de procesamiento, estados de citas, reprogramaciones, cancelaciones, lista de espera, como se detalla en las diversas interfaces de calendario y gestión de citas.
Gestión Multi-Estilista:
Agenda con disponibilidad y citas de múltiples estilistas.
Asignación inteligente de citas.
Vista consolidada para administradores/recepcionistas.
(Futuro) Integración con Pagos: Solicitud de depósitos o cobro de servicios.
Consideración UX: Visualización clara de disponibilidad, manejo de conflictos, proceso de reserva ágil.
4.5. Gestión de Inventario Proactiva y Detallada
El usuario podrá tendrá un catálogo de las 100 marcas de coloración más reconocidas en el mundo con sus correspondientes lineas de producto. Y prodrás seleccionar la/s marca/s y linea/s con las que trabaja.
También podrá introductor individualmente y gestionar el inventario de cada producto con lo que trabaja en el salón coloración, tratamientos, styling, reventa, herramientas), como se muestra en las pantallas de inventario con filtros por tipo.
4.6. Catálogo de Servicios del Salón y Precios Flexibles
Creación y personalización ilimitada de servicios, visible en las opciones de "Acciones Rápidas".
Asignación de precio base, duración, productos estándar.
Posibilidad de precios variables según diagnóstico IA (longitud, densidad).
Agrupación de servicios en paquetes.
Consideración de Rentabilidad: Plataforma podría ayudar a calcular rentabilidad por servicio.
4.7. Analíticas Avanzadas y Reportes de Negocio Comprensivos (Visión Futura)

Dashboard principal con KPIs personalizables (ingresos, citas, ocupación, clientes nuevos, ticket promedio, stock bajo), como se muestra en las interfaces de dashboard.
Reportes financieros detallados.
Análisis de clientes profundo (frecuencia, gasto, retención, LTV, segmentación), como se visualiza en las pantallas de "Análisis de Clientes" con métricas y gráficos.
Reportes de uso de inventario.
Análisis de rendimiento de estilistas.
Análisis de popularidad de servicios y productos.
Filtros avanzados y exportación de datos.
Benchmarking (Anonimizado y Agregado - Futuro Lejano y con Consentimiento): Comparativas de rendimiento anonimizadas.
Optimización de Costes y Rendimiento: Uso de vistas materializadas o pre-cálculo de agregaciones para reportes.
4.8. Módulo de Formación y Comunidad (Visión Futura)
Biblioteca de recursos: tutoriales, guías, novedades.
Foro para estilistas.
Actualizaciones sobre tendencias.
Certificaciones y Seguimiento de Progreso: Para academias o formación interna.
4.9. Portal/App para Cliente Final (Visión Futura)
Clientes acceden a su historial, gestionan citas, reciben recomendaciones.
Consideración UX: Interfaz simple enfocada en necesidades del cliente.
4.10. Módulo de Gestión de Personal (Para Salones)
Perfiles de empleados (estilistas, asistentes).
Asignación de roles y permisos (vinculado a 4.1).
Gestión de horarios de trabajo y turnos.
(Futuro) Cálculo de comisiones o seguimiento de objetivos.
5. Arquitectura Técnica (Visión Completa)
5.1. Frontend:
Aplicación Móvil Principal (Estilistas): React Native con Expo SDK.
(Futuro Opcional) Aplicación Web de Administración.
(Futuro Opcional) Aplicación Móvil/PWA (Clientes).
Componentización robusta, gestión de estado escalable (Context API inicialmente, considerar Zustand/Jotai/Redux Toolkit para complejidad mayor).
Optimización de Rendimiento Percibido: Uso de esqueletos de UI, carga progresiva, actualizaciones optimistas, virtualización de listas.
Se añadirán librerías para detección facial y manipulación de imágenes en cliente si se opta por esa vía para el difuminado.
Almacenamiento Local en Cliente: Uso de AsyncStorage o MMKV para caché de datos de sesión, preferencias de usuario, o datos para funcionamiento offline básico.
5.2. Backend:
Plataforma Principal: Supabase (Auth, Database, Storage, Edge Functions).
(Futuro Opcional) Microservicios Adicionales.
Base de datos PostgreSQL optimizada.
Estrategias de caché robustas.
Consideración de Escalabilidad y Costes:
Arquitectura Orientada a Eventos (Opcional para ciertas partes): Para desacoplar servicios y mejorar resiliencia/escalabilidad.
Optimización de Base de Datos Continua: Monitoreo de consultas, re-evaluación de índices.
Optimización de Costes (Supabase): Elección de plan adecuado, optimización de consultas, políticas de almacenamiento, monitoreo de recursos.
Seguridad en Edge Functions: Validar entradas, aplicar permisos, manejo de errores.
5.3. Integración IA:
OpenAI (GPT-4 Vision, GPT-4 Turbo, y futuros modelos).
Cumplimiento de Políticas de API: Adhesión estricta a términos de OpenAI, incluyendo manejo de datos personales e imágenes (pre-procesamiento es clave).
Orquestación de IA: Diseño de flujos con múltiples llamadas a modelos.
(Futuro) Afinamiento (Fine-tuning) de Modelos.
IA Explicable (XAI).
Escalabilidad de la IA:
Gestión de Carga de API: Colas y reintentos para picos de demanda.
Balanceo entre Modelos: Capacidad de cambiar dinámicamente a modelos alternativos.
Personalización de la IA por Salón/Estilista (Avanzado): Aprendizaje de IA de preferencias del estilista.
Optimización de Costes y Rendimiento IA: Procesamiento por lotes (si aplica), caching de respuestas IA, monitoreo activo de costes de API.
6. Modelo de Datos (Visión Completa)
El esquema de base de datos deberá expandirse significativamente para soportar todas estas funcionalidades, incluyendo campos para consentimiento, logs de auditoría, indicadores de anonimización de imágenes, criterios de segmentación, plantillas de comunicación, movimientos de inventario detallados, configuraciones de precios dinámicos, gestión de roles/permisos, y datos para analíticas y benchmarking.
Consideración de Costes: Diseño para minimizar redundancia y optimizar consultas.
Logs de Auditoría: Tablas para logs de acciones críticas o acceso a datos sensibles.
7. Requisitos No Funcionales (Visión Completa)
Rendimiento Superior y Escalabilidad
Seguridad de Grado Empresarial (Extendido):
Validación de Entradas Rigurosa (frontend y backend).
Seguridad de la Aplicación Móvil (almacenamiento seguro en dispositivo con expo-secure-store, protección básica contra ingeniería inversa).
Gestión de Sesiones Segura (tokens JWT, políticas de expiración, invalidación).
Pruebas de Seguridad Regulares (pen-testing, revisión de vulnerabilidades).
Privacidad y Cumplimiento Normativo (Extendido):
Diseño Orientado a la Privacidad (Privacy by Design and by Default).
Cumplimiento de Regulaciones Específicas (GDPR, CCPA, etc.): transparencia, bases legales, derechos de los interesados, notificación de brechas, EIPD si es necesario.
Términos de Servicio y Política de Privacidad Detallados y accesibles.
Minimización de Datos.
Seguridad de los Datos (medidas técnicas y organizativas).
Alta Disponibilidad y Fiabilidad (Extendido):
(99.9%+ uptime).
Plan de Recuperación ante Desastres (DRP) y Continuidad del Negocio (BCP): Documentar y probar procedimientos.
Personalización Extrema y Flexibilidad
Capacidad de Integración Robusta (Extendido):
API para Desarrolladores (Futuro Lejano).
Internacionalización (i18n) y Localización (l10n): Planificar desde el inicio.
Experiencia de Usuario (UX) Excepcional (Extendido):
Onboarding Guiado y Contextual.
Ayuda Integrada y FAQs.
Accesibilidad (a11y): Considerar pautas WCAG.
Diseño Adaptativo y Progresivo: Revelar complejidad gradualmente.
Mantenibilidad y Evolución del Código (Extendido):
Pruebas Automatizadas (unitarias, integración, E2E futuro).
Documentación Técnica actualizada.
Manejo de Errores y Resiliencia Detallado:
Mecanismos de reintento inteligentes.
Mensajes de error claros y accionables.
Logging estructurado en backend.
Operaciones Offline (Consideración Estratégica):
Identificar funcionalidades clave para offline (visualización de datos cacheados, toma de notas).
Estrategia de sincronización (manejo de conflictos).
8. Consideraciones Adicionales (Visión Completa)
Estrategia de Monetización:
Modelos de suscripción, cobro por uso de IA avanzado, etc.
Soporte al Cliente y Éxito del Cliente (Extendido):
Niveles de Soporte.
Comunidad de Usuarios Activa.
Aspectos Legales y Éticos de la IA (Extendido):
Transparencia, gestión de sesgos, propiedad de datos, responsabilidad y descargos (IA como herramienta de apoyo).
Propiedad Intelectual: Clarificar en Términos de Servicio la propiedad de datos.
Estrategia de Datos y Privacidad (Extendido):
Políticas robustas, consentimiento, anonimización, retención de datos.
Hoja de Ruta Tecnológica:
Planificación para adopción de nuevas tecnologías.
Recopilación de Feedback Continuo y Co-creación:
Mecanismos en la app, encuestas.
Involucrar a un panel de estilistas asesores.
Estrategia de Despliegue e Iteración (Extendido):
CI/CD (EAS Build/Submit).
Lanzamientos Canary o por Funcionalidades (Feature Flags).
Plan de Formación y Adopción para Salones:
Materiales y programas para ayudar a los salones a integrar Salonier.
Consideraciones sobre el Ecosistema de la Industria de la Belleza:
Interacción o complementariedad con otras herramientas de salones.
Oportunidades de asociación con marcas o instituciones educativas.
