# Salonier - Aplicación Móvil

Salonier es una plataforma tecnológica integral para profesionales de la coloración capilar y gestión de salones de belleza. Fusiona la profesión de la peluquería con la ciencia y la inteligencia artificial.

## 🚀 Características Principales

- **Autenticación Segura**: Login con email/contraseña y redes sociales
- **Gestión de Clientes**: Perfiles 360°, historial completo, segmentación
- **Consulta IA**: Diagnóstico capilar asistido por GPT-4 Vision
- **Agenda Inteligente**: Calendario interactivo multi-estilista
- **Inventario**: Gestión de productos y marcas de coloración
- **Dashboard**: KPIs y analíticas en tiempo real
- **Roles y Permisos**: Administrador, Estilista, Recepcionista

## 🛠️ Stack Tecnológico

- **Frontend**: React Native con Expo SDK
- **Backend**: Supabase (Auth, Database, Storage, Edge Functions)
- **Base de Datos**: PostgreSQL
- **IA**: OpenAI GPT-4 Vision
- **Navegación**: React Navigation
- **Estado**: Context API
- **Estilos**: StyleSheet nativo

## 📱 Instalación y Configuración

### Prerrequisitos

- Node.js (v18 o superior)
- npm o yarn
- Expo CLI
- Cuenta de Supabase
- Cuenta de OpenAI (opcional)

### Pasos de Instalación

1. **Clonar el repositorio**
   ```bash
   git clone <repository-url>
   cd salonier-app
   ```

2. **Instalar dependencias**
   ```bash
   npm install
   ```

3. **Configurar variables de entorno**
   ```bash
   cp .env.example .env
   ```
   
   Editar `.env` con tus credenciales:
   ```
   EXPO_PUBLIC_SUPABASE_URL=tu_url_de_supabase
   EXPO_PUBLIC_SUPABASE_ANON_KEY=tu_clave_anonima_de_supabase
   EXPO_PUBLIC_OPENAI_API_KEY=tu_clave_de_openai
   ```

4. **Configurar Supabase**
   - Crear proyecto en [Supabase](https://supabase.com)
   - Ejecutar las migraciones de base de datos (ver `/supabase/migrations`)
   - Configurar políticas RLS (Row Level Security)

5. **Ejecutar la aplicación**
   ```bash
   npm start
   ```

## 🏗️ Estructura del Proyecto

```
src/
├── contexts/          # Contextos de React (Auth, etc.)
├── lib/              # Configuraciones y utilidades
├── navigation/       # Configuración de navegación
├── screens/          # Pantallas de la aplicación
│   ├── auth/         # Autenticación
│   ├── dashboard/    # Dashboard principal
│   ├── clients/      # Gestión de clientes
│   ├── appointments/ # Gestión de citas
│   ├── consultation/ # Consulta IA
│   ├── inventory/    # Inventario
│   └── profile/      # Perfil de usuario
├── types/            # Definiciones de TypeScript
└── components/       # Componentes reutilizables
```

## 🔧 Scripts Disponibles

- `npm start` - Inicia el servidor de desarrollo
- `npm run android` - Ejecuta en Android
- `npm run ios` - Ejecuta en iOS
- `npm run web` - Ejecuta en navegador web

## 🔐 Configuración de Seguridad

### Supabase RLS Policies

La aplicación utiliza Row Level Security (RLS) para garantizar que los usuarios solo accedan a sus datos autorizados:

- Los usuarios solo pueden ver/editar sus propios datos
- Los administradores de salón pueden gestionar todos los datos del salón
- Los estilistas solo ven sus clientes y citas asignadas

### Privacidad de Imágenes

- Detección y difuminado automático de rostros
- Cumplimiento con políticas de OpenAI
- Almacenamiento seguro en Supabase Storage

## 🤖 Integración con IA

### GPT-4 Vision

- Análisis automático de imágenes capilares
- Identificación de nivel natural, subtono, canas
- Evaluación de porosidad, elasticidad, resistencia
- Generación de fórmulas de coloración

### Optimización de Costes

- Prompts específicos y dirigidos
- Compresión inteligente de imágenes
- Caché de respuestas IA
- Monitoreo activo de costes

## 📊 Base de Datos

### Tablas Principales

- `users` - Usuarios del sistema
- `salons` - Información de salones
- `clients` - Clientes del salón
- `appointments` - Citas programadas
- `services` - Servicios ofrecidos
- `products` - Inventario de productos
- `hair_diagnoses` - Diagnósticos capilares
- `color_formulations` - Fórmulas de coloración

## 🚀 Despliegue

### Desarrollo
```bash
npm start
```

### Producción
```bash
# Build para Android
eas build --platform android

# Build para iOS
eas build --platform ios

# Submit a stores
eas submit --platform all
```

## 🤝 Contribución

1. Fork el proyecto
2. Crear una rama para tu feature (`git checkout -b feature/AmazingFeature`)
3. Commit tus cambios (`git commit -m 'Add some AmazingFeature'`)
4. Push a la rama (`git push origin feature/AmazingFeature`)
5. Abrir un Pull Request

## 📄 Licencia

Este proyecto está bajo la Licencia MIT. Ver `LICENSE` para más detalles.

## 📞 Soporte

Para soporte técnico o preguntas:
- Email: <EMAIL>
- Documentación: [docs.salonier.com](https://docs.salonier.com)

## 🔄 Roadmap

### Fase 1 (Actual)
- ✅ Configuración inicial del proyecto
- ✅ Sistema de autenticación
- ✅ Navegación básica
- ✅ Dashboard principal
- ✅ Gestión básica de clientes

### Fase 2 (Próxima)
- 🔄 Flujo completo de consulta IA
- 🔄 Sistema de citas avanzado
- 🔄 Gestión de inventario
- 🔄 Analíticas y reportes

### Fase 3 (Futuro)
- ⏳ App para clientes finales
- ⏳ Módulo de formación
- ⏳ Integración con sistemas de pago
- ⏳ API pública para desarrolladores
