import { createClient } from '@supabase/supabase-js';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Configuración de Supabase
const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL || 'https://demo.supabase.co';
const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY || 'demo-key';

// Verificar si las credenciales están configuradas
const isConfigured = supabaseUrl !== 'https://demo.supabase.co' && supabaseAnonKey !== 'demo-key';

// Crear cliente de Supabase con configuración para React Native
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    storage: AsyncStorage,
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
});

export { isConfigured };

// Tipos para las tablas de la base de datos
export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string;
          email: string;
          full_name: string;
          phone: string | null;
          avatar_url: string | null;
          role: 'admin' | 'stylist' | 'receptionist';
          salon_id: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          email: string;
          full_name: string;
          phone?: string | null;
          avatar_url?: string | null;
          role?: 'admin' | 'stylist' | 'receptionist';
          salon_id?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          email?: string;
          full_name?: string;
          phone?: string | null;
          avatar_url?: string | null;
          role?: 'admin' | 'stylist' | 'receptionist';
          salon_id?: string | null;
          updated_at?: string;
        };
      };
      salons: {
        Row: {
          id: string;
          name: string;
          address: string;
          phone: string;
          email: string;
          owner_id: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          address: string;
          phone: string;
          email: string;
          owner_id: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          address?: string;
          phone?: string;
          email?: string;
          owner_id?: string;
          updated_at?: string;
        };
      };
      clients: {
        Row: {
          id: string;
          full_name: string;
          email: string | null;
          phone: string | null;
          birthday: string | null;
          notes: string | null;
          salon_id: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          full_name: string;
          email?: string | null;
          phone?: string | null;
          birthday?: string | null;
          notes?: string | null;
          salon_id: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          full_name?: string;
          email?: string | null;
          phone?: string | null;
          birthday?: string | null;
          notes?: string | null;
          salon_id?: string;
          updated_at?: string;
        };
      };
      appointments: {
        Row: {
          id: string;
          client_id: string;
          stylist_id: string;
          salon_id: string;
          service_ids: string[];
          start_time: string;
          end_time: string;
          status: 'scheduled' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled';
          notes: string | null;
          total_price: number | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          client_id: string;
          stylist_id: string;
          salon_id: string;
          service_ids: string[];
          start_time: string;
          end_time: string;
          status?: 'scheduled' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled';
          notes?: string | null;
          total_price?: number | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          client_id?: string;
          stylist_id?: string;
          salon_id?: string;
          service_ids?: string[];
          start_time?: string;
          end_time?: string;
          status?: 'scheduled' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled';
          notes?: string | null;
          total_price?: number | null;
          updated_at?: string;
        };
      };
      services: {
        Row: {
          id: string;
          name: string;
          description: string | null;
          base_price: number;
          duration_minutes: number;
          salon_id: string;
          category: 'color' | 'cut' | 'treatment' | 'styling' | 'other';
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          description?: string | null;
          base_price: number;
          duration_minutes: number;
          salon_id: string;
          category: 'color' | 'cut' | 'treatment' | 'styling' | 'other';
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          description?: string | null;
          base_price?: number;
          duration_minutes?: number;
          salon_id?: string;
          category?: 'color' | 'cut' | 'treatment' | 'styling' | 'other';
          updated_at?: string;
        };
      };
      products: {
        Row: {
          id: string;
          name: string;
          brand: string;
          category: 'color' | 'developer' | 'treatment' | 'styling' | 'tools';
          sku: string | null;
          current_stock: number;
          min_stock: number;
          unit_price: number;
          salon_id: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          brand: string;
          category: 'color' | 'developer' | 'treatment' | 'styling' | 'tools';
          sku?: string | null;
          current_stock: number;
          min_stock: number;
          unit_price: number;
          salon_id: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          brand?: string;
          category?: 'color' | 'developer' | 'treatment' | 'styling' | 'tools';
          sku?: string | null;
          current_stock?: number;
          min_stock?: number;
          unit_price?: number;
          salon_id?: string;
          updated_at?: string;
        };
      };
    };
  };
}
