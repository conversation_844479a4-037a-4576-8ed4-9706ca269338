import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { isConfigured } from '../lib/supabase';

interface DemoModeAlertProps {
  onDismiss?: () => void;
}

export const DemoModeAlert: React.FC<DemoModeAlertProps> = ({ onDismiss }) => {
  if (isConfigured) {
    return null;
  }

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <Ionicons name="information-circle" size={24} color="#f59e0b" />
        <View style={styles.textContainer}>
          <Text style={styles.title}>Modo Demo</Text>
          <Text style={styles.message}>
            La aplicación está funcionando en modo demo. Para usar todas las funcionalidades,
            configura Supabase en el archivo .env
          </Text>
        </View>
        {onDismiss && (
          <TouchableOpacity onPress={onDismiss} style={styles.dismissButton}>
            <Ionicons name="close" size={20} color="#6b7280" />
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fef3c7',
    borderWidth: 1,
    borderColor: '#f59e0b',
    borderRadius: 8,
    margin: 16,
    padding: 12,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  textContainer: {
    flex: 1,
    marginLeft: 12,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: '#92400e',
    marginBottom: 4,
  },
  message: {
    fontSize: 14,
    color: '#92400e',
    lineHeight: 20,
  },
  dismissButton: {
    padding: 4,
    marginLeft: 8,
  },
});
