// Tipos principales de la aplicación Salonier

export interface User {
  id: string;
  email: string;
  full_name: string;
  phone?: string;
  avatar_url?: string;
  role: UserRole;
  salon_id?: string;
  created_at: string;
  updated_at: string;
}

export type UserRole = 'admin' | 'stylist' | 'receptionist';

export interface Salon {
  id: string;
  name: string;
  address: string;
  phone: string;
  email: string;
  owner_id: string;
  created_at: string;
  updated_at: string;
}

export interface Client {
  id: string;
  full_name: string;
  email?: string;
  phone?: string;
  birthday?: string;
  notes?: string;
  salon_id: string;
  created_at: string;
  updated_at: string;
}

export interface HairDiagnosis {
  id: string;
  client_id: string;
  natural_level: number;
  undertone: string;
  gray_percentage: number;
  diameter: 'fine' | 'medium' | 'coarse';
  density: 'low' | 'medium' | 'high';
  porosity: 'low' | 'medium' | 'high';
  elasticity: 'poor' | 'normal' | 'good';
  resistance: 'low' | 'medium' | 'high';
  existing_color?: string;
  chemical_history: ChemicalTreatment[];
  images: DiagnosisImage[];
  created_at: string;
  updated_at: string;
}

export interface ChemicalTreatment {
  id: string;
  type: 'color' | 'bleach' | 'perm' | 'relaxer' | 'other';
  product_used?: string;
  date: string;
  notes?: string;
}

export interface DiagnosisImage {
  id: string;
  url: string;
  zone: 'frontal' | 'crown' | 'sides' | 'nape' | 'full';
  is_anonymized: boolean;
}

export interface ColorFormulation {
  id: string;
  diagnosis_id: string;
  target_color: string;
  formula: FormulaComponent[];
  processing_time: number;
  instructions: string;
  ai_confidence: number;
  stylist_notes?: string;
  created_at: string;
  updated_at: string;
}

export interface FormulaComponent {
  product_id: string;
  product_name: string;
  brand: string;
  quantity: number;
  unit: 'ml' | 'g' | 'oz';
}

export interface Appointment {
  id: string;
  client_id: string;
  stylist_id: string;
  salon_id: string;
  service_ids: string[];
  start_time: string;
  end_time: string;
  status: 'scheduled' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled';
  notes?: string;
  total_price?: number;
  created_at: string;
  updated_at: string;
}

export interface Service {
  id: string;
  name: string;
  description?: string;
  base_price: number;
  duration_minutes: number;
  salon_id: string;
  category: 'color' | 'cut' | 'treatment' | 'styling' | 'other';
  created_at: string;
  updated_at: string;
}

export interface Product {
  id: string;
  name: string;
  brand: string;
  category: 'color' | 'developer' | 'treatment' | 'styling' | 'tools';
  sku?: string;
  current_stock: number;
  min_stock: number;
  unit_price: number;
  salon_id: string;
  created_at: string;
  updated_at: string;
}

export interface Brand {
  id: string;
  name: string;
  logo_url?: string;
  product_lines: ProductLine[];
}

export interface ProductLine {
  id: string;
  name: string;
  brand_id: string;
  category: string;
  products: Product[];
}

// Tipos para navegación
export type RootStackParamList = {
  Auth: undefined;
  Main: undefined;
  Login: undefined;
  Register: undefined;
  ForgotPassword: undefined;
};

export type MainTabParamList = {
  Dashboard: undefined;
  Clients: undefined;
  Appointments: undefined;
  Consultation: undefined;
  Inventory: undefined;
  Profile: undefined;
};

export type ClientStackParamList = {
  ClientList: undefined;
  ClientDetail: { clientId: string };
  ClientForm: { clientId?: string };
  ClientHistory: { clientId: string };
};

export type ConsultationStackParamList = {
  ClientSelection: undefined;
  Diagnosis: { clientId: string };
  ColorSelection: { clientId: string; diagnosisId: string };
  Formulation: { clientId: string; diagnosisId: string; targetColor: string };
  Results: { clientId: string; formulationId: string };
};

// Tipos para el contexto de autenticación
export interface AuthContextType {
  user: User | null;
  session: any;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string, userData: Partial<User>) => Promise<void>;
  signOut: () => Promise<void>;
  updateProfile: (userData: Partial<User>) => Promise<void>;
}

// Tipos para configuración de la app
export interface AppConfig {
  supabaseUrl: string;
  supabaseAnonKey: string;
  openaiApiKey?: string;
}

// Tipos para respuestas de IA
export interface AIAnalysisResponse {
  diagnosis: Partial<HairDiagnosis>;
  confidence: number;
  recommendations: string[];
  warnings?: string[];
}

export interface AIFormulationResponse {
  formula: FormulaComponent[];
  instructions: string;
  processing_time: number;
  confidence: number;
  alternatives?: FormulaComponent[][];
  warnings?: string[];
}
