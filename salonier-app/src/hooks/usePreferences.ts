import { useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface UserPreferences {
  // Notificaciones
  notifications_appointments: boolean;
  notifications_inventory: boolean;
  notifications_clients: boolean;
  
  // Preferencias de la app
  preferred_brands: string[];
  units: 'metric' | 'imperial';
  language: 'es' | 'en' | 'fr' | 'it';
  theme: 'light' | 'dark' | 'auto';
  
  // Datos y privacidad
  auto_backup: boolean;
  analytics: boolean;
  
  // Configuración profesional
  default_processing_time: number; // en minutos
  default_consultation_duration: number; // en minutos
  show_ai_confidence: boolean;
  auto_save_formulations: boolean;
}

const DEFAULT_PREFERENCES: UserPreferences = {
  notifications_appointments: true,
  notifications_inventory: true,
  notifications_clients: false,
  preferred_brands: ['L\'Oréal Professional', 'Wella Professionals'],
  units: 'metric',
  language: 'es',
  theme: 'light',
  auto_backup: true,
  analytics: false,
  default_processing_time: 30,
  default_consultation_duration: 60,
  show_ai_confidence: true,
  auto_save_formulations: true,
};

const PREFERENCES_STORAGE_KEY = '@salonier_preferences';

export const usePreferences = () => {
  const [preferences, setPreferences] = useState<UserPreferences>(DEFAULT_PREFERENCES);
  const [loading, setLoading] = useState(true);

  // Cargar preferencias al inicializar
  useEffect(() => {
    loadPreferences();
  }, []);

  const loadPreferences = async () => {
    try {
      const stored = await AsyncStorage.getItem(PREFERENCES_STORAGE_KEY);
      if (stored) {
        const parsedPreferences = JSON.parse(stored);
        // Merge con valores por defecto para asegurar que todas las propiedades existan
        setPreferences({ ...DEFAULT_PREFERENCES, ...parsedPreferences });
      }
    } catch (error) {
      console.error('Error loading preferences:', error);
    } finally {
      setLoading(false);
    }
  };

  const savePreferences = async (newPreferences: Partial<UserPreferences>) => {
    try {
      const updatedPreferences = { ...preferences, ...newPreferences };
      await AsyncStorage.setItem(PREFERENCES_STORAGE_KEY, JSON.stringify(updatedPreferences));
      setPreferences(updatedPreferences);
      return true;
    } catch (error) {
      console.error('Error saving preferences:', error);
      return false;
    }
  };

  const updatePreference = async <K extends keyof UserPreferences>(
    key: K,
    value: UserPreferences[K]
  ) => {
    return savePreferences({ [key]: value });
  };

  const resetPreferences = async () => {
    try {
      await AsyncStorage.removeItem(PREFERENCES_STORAGE_KEY);
      setPreferences(DEFAULT_PREFERENCES);
      return true;
    } catch (error) {
      console.error('Error resetting preferences:', error);
      return false;
    }
  };

  const addPreferredBrand = async (brand: string) => {
    if (!preferences.preferred_brands.includes(brand)) {
      const newBrands = [...preferences.preferred_brands, brand];
      return updatePreference('preferred_brands', newBrands);
    }
    return true;
  };

  const removePreferredBrand = async (brand: string) => {
    const newBrands = preferences.preferred_brands.filter(b => b !== brand);
    return updatePreference('preferred_brands', newBrands);
  };

  // Helpers para obtener valores formateados
  const getFormattedUnit = (type: 'volume' | 'weight') => {
    if (preferences.units === 'metric') {
      return type === 'volume' ? 'ml' : 'g';
    } else {
      return type === 'volume' ? 'fl oz' : 'oz';
    }
  };

  const convertUnit = (value: number, type: 'volume' | 'weight', toMetric: boolean = true) => {
    if (preferences.units === 'metric' && toMetric) return value;
    if (preferences.units === 'imperial' && !toMetric) return value;

    if (type === 'volume') {
      // ml <-> fl oz
      return toMetric ? value * 29.5735 : value / 29.5735;
    } else {
      // g <-> oz
      return toMetric ? value * 28.3495 : value / 28.3495;
    }
  };

  const getLanguageLabel = () => {
    const labels = {
      es: 'Español',
      en: 'English',
      fr: 'Français',
      it: 'Italiano',
    };
    return labels[preferences.language];
  };

  const getThemeLabel = () => {
    const labels = {
      light: 'Claro',
      dark: 'Oscuro',
      auto: 'Automático',
    };
    return labels[preferences.theme];
  };

  const getUnitsLabel = () => {
    const labels = {
      metric: 'Métrico (ml, g)',
      imperial: 'Imperial (fl oz, oz)',
    };
    return labels[preferences.units];
  };

  return {
    preferences,
    loading,
    savePreferences,
    updatePreference,
    resetPreferences,
    addPreferredBrand,
    removePreferredBrand,
    getFormattedUnit,
    convertUnit,
    getLanguageLabel,
    getThemeLabel,
    getUnitsLabel,
  };
};
