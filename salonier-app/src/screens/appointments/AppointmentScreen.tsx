import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  FlatList,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Appointment } from '../../types';

interface AppointmentWithClient extends Appointment {
  client_name: string;
  stylist_name: string;
  service_names: string[];
}

export const AppointmentScreen: React.FC = () => {
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [appointments, setAppointments] = useState<AppointmentWithClient[]>([]);
  const [viewMode, setViewMode] = useState<'day' | 'week' | 'month'>('day');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadAppointments();
  }, [selectedDate]);

  const loadAppointments = async () => {
    try {
      // Simular carga de citas
      const mockAppointments: AppointmentWithClient[] = [
        {
          id: '1',
          client_id: '1',
          client_name: '<PERSON>',
          stylist_id: '1',
          stylist_name: '<PERSON>',
          salon_id: '1',
          service_ids: ['1', '2'],
          service_names: ['Coloración', 'Corte'],
          start_time: '2024-06-18T09:00:00Z',
          end_time: '2024-06-18T11:00:00Z',
          status: 'confirmed',
          total_price: 85,
          created_at: '2024-06-17T10:00:00Z',
          updated_at: '2024-06-17T10:00:00Z',
        },
        {
          id: '2',
          client_id: '2',
          client_name: 'Carmen López',
          stylist_id: '1',
          stylist_name: 'Ana López',
          salon_id: '1',
          service_ids: ['3'],
          service_names: ['Mechas'],
          start_time: '2024-06-18T11:30:00Z',
          end_time: '2024-06-18T14:00:00Z',
          status: 'scheduled',
          total_price: 95,
          created_at: '2024-06-17T10:00:00Z',
          updated_at: '2024-06-17T10:00:00Z',
        },
        {
          id: '3',
          client_id: '3',
          client_name: 'Isabel Ruiz',
          stylist_id: '2',
          stylist_name: 'Laura Martín',
          salon_id: '1',
          service_ids: ['4'],
          service_names: ['Balayage'],
          start_time: '2024-06-18T15:00:00Z',
          end_time: '2024-06-18T17:30:00Z',
          status: 'confirmed',
          total_price: 120,
          created_at: '2024-06-17T10:00:00Z',
          updated_at: '2024-06-17T10:00:00Z',
        },
      ];
      setAppointments(mockAppointments);
    } catch (error) {
      console.error('Error loading appointments:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString('es-ES', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed': return '#10b981';
      case 'scheduled': return '#f59e0b';
      case 'in_progress': return '#6366f1';
      case 'completed': return '#6b7280';
      case 'cancelled': return '#ef4444';
      default: return '#9ca3af';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'confirmed': return 'Confirmada';
      case 'scheduled': return 'Programada';
      case 'in_progress': return 'En curso';
      case 'completed': return 'Completada';
      case 'cancelled': return 'Cancelada';
      default: return status;
    }
  };

  const handleAddAppointment = () => {
    Alert.alert('Nueva cita', 'Funcionalidad próximamente disponible');
  };

  const handleAppointmentPress = (appointment: AppointmentWithClient) => {
    Alert.alert(
      appointment.client_name,
      `${appointment.service_names.join(', ')}\n${formatTime(appointment.start_time)} - ${formatTime(appointment.end_time)}\nEstilista: ${appointment.stylist_name}`,
      [
        { text: 'Cancelar', style: 'cancel' },
        { text: 'Editar', onPress: () => console.log('Edit appointment') },
        { text: 'Ver detalles', onPress: () => console.log('View details') },
      ]
    );
  };

  const renderAppointment = ({ item }: { item: AppointmentWithClient }) => (
    <TouchableOpacity
      style={styles.appointmentCard}
      onPress={() => handleAppointmentPress(item)}
    >
      <View style={styles.appointmentTime}>
        <Text style={styles.timeText}>{formatTime(item.start_time)}</Text>
        <Text style={styles.durationText}>
          {Math.round((new Date(item.end_time).getTime() - new Date(item.start_time).getTime()) / (1000 * 60))}min
        </Text>
      </View>

      <View style={styles.appointmentContent}>
        <Text style={styles.clientName}>{item.client_name}</Text>
        <Text style={styles.serviceText}>{item.service_names.join(', ')}</Text>
        <Text style={styles.stylistText}>con {item.stylist_name}</Text>
      </View>

      <View style={styles.appointmentRight}>
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}>
          <Text style={styles.statusText}>{getStatusLabel(item.status)}</Text>
        </View>
        {item.total_price && (
          <Text style={styles.priceText}>€{item.total_price}</Text>
        )}
      </View>
    </TouchableOpacity>
  );

  const getDayAppointments = () => {
    const dayStart = new Date(selectedDate);
    dayStart.setHours(0, 0, 0, 0);
    const dayEnd = new Date(selectedDate);
    dayEnd.setHours(23, 59, 59, 999);

    return appointments.filter(apt => {
      const aptDate = new Date(apt.start_time);
      return aptDate >= dayStart && aptDate <= dayEnd;
    });
  };

  const dayAppointments = getDayAppointments();

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Agenda</Text>
        <TouchableOpacity style={styles.addButton} onPress={handleAddAppointment}>
          <Ionicons name="add" size={24} color="white" />
        </TouchableOpacity>
      </View>

      {/* Date Navigation */}
      <View style={styles.dateNavigation}>
        <TouchableOpacity
          style={styles.navButton}
          onPress={() => {
            const newDate = new Date(selectedDate);
            newDate.setDate(newDate.getDate() - 1);
            setSelectedDate(newDate);
          }}
        >
          <Ionicons name="chevron-back" size={24} color="#6366f1" />
        </TouchableOpacity>

        <View style={styles.dateContainer}>
          <Text style={styles.dateText}>
            {selectedDate.toLocaleDateString('es-ES', {
              weekday: 'long',
              day: 'numeric',
              month: 'long',
            })}
          </Text>
          <Text style={styles.dayText}>
            {dayAppointments.length} cita{dayAppointments.length !== 1 ? 's' : ''}
          </Text>
        </View>

        <TouchableOpacity
          style={styles.navButton}
          onPress={() => {
            const newDate = new Date(selectedDate);
            newDate.setDate(newDate.getDate() + 1);
            setSelectedDate(newDate);
          }}
        >
          <Ionicons name="chevron-forward" size={24} color="#6366f1" />
        </TouchableOpacity>
      </View>

      {/* Quick Stats */}
      <View style={styles.statsContainer}>
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>{dayAppointments.length}</Text>
          <Text style={styles.statLabel}>Citas</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>
            €{dayAppointments.reduce((sum, apt) => sum + (apt.total_price || 0), 0)}
          </Text>
          <Text style={styles.statLabel}>Ingresos</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>
            {dayAppointments.filter(apt => apt.status === 'confirmed').length}
          </Text>
          <Text style={styles.statLabel}>Confirmadas</Text>
        </View>
      </View>

      {/* Appointments List */}
      <FlatList
        data={dayAppointments}
        renderItem={renderAppointment}
        keyExtractor={(item) => item.id}
        style={styles.appointmentsList}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="calendar-outline" size={64} color="#d1d5db" />
            <Text style={styles.emptyTitle}>No hay citas</Text>
            <Text style={styles.emptySubtitle}>
              No tienes citas programadas para este día
            </Text>
            <TouchableOpacity style={styles.emptyButton} onPress={handleAddAppointment}>
              <Text style={styles.emptyButtonText}>Agregar cita</Text>
            </TouchableOpacity>
          </View>
        }
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    paddingTop: 60,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  addButton: {
    backgroundColor: '#6366f1',
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
  },
  dateNavigation: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  navButton: {
    padding: 8,
  },
  dateContainer: {
    alignItems: 'center',
  },
  dateText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
    textTransform: 'capitalize',
  },
  dayText: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 2,
  },
  statsContainer: {
    flexDirection: 'row',
    backgroundColor: 'white',
    paddingVertical: 16,
    marginBottom: 20,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  statLabel: {
    fontSize: 12,
    color: '#6b7280',
    marginTop: 4,
  },
  appointmentsList: {
    flex: 1,
    paddingHorizontal: 20,
  },
  appointmentCard: {
    flexDirection: 'row',
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  appointmentTime: {
    alignItems: 'center',
    marginRight: 16,
    minWidth: 60,
  },
  timeText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
  },
  durationText: {
    fontSize: 12,
    color: '#6b7280',
    marginTop: 2,
  },
  appointmentContent: {
    flex: 1,
  },
  clientName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 2,
  },
  serviceText: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 2,
  },
  stylistText: {
    fontSize: 12,
    color: '#9ca3af',
  },
  appointmentRight: {
    alignItems: 'flex-end',
    justifyContent: 'space-between',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginBottom: 4,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
    color: 'white',
  },
  priceText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6366f1',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#374151',
    marginTop: 16,
  },
  emptySubtitle: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 8,
    textAlign: 'center',
    marginBottom: 24,
  },
  emptyButton: {
    backgroundColor: '#6366f1',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  emptyButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});
