import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
  TextInput,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface Client {
  id: string;
  name: string;
}

interface Service {
  id: string;
  name: string;
  duration: number;
  price: number;
}

interface Stylist {
  id: string;
  name: string;
}

export const AppointmentFormScreen: React.FC = () => {
  const [selectedClient, setSelectedClient] = useState<Client | null>(null);
  const [selectedServices, setSelectedServices] = useState<Service[]>([]);
  const [selectedStylist, setSelectedStylist] = useState<Stylist | null>(null);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [selectedTime, setSelectedTime] = useState('09:00');
  const [notes, setNotes] = useState('');
  const [loading, setLoading] = useState(false);

  const [clients] = useState<Client[]>([
    { id: '1', name: '<PERSON>' },
    { id: '2', name: '<PERSON>' },
    { id: '3', name: '<PERSON>' },
  ]);

  const [services] = useState<Service[]>([
    { id: '1', name: 'Coloración', duration: 90, price: 65 },
    { id: '2', name: 'Corte', duration: 30, price: 25 },
    { id: '3', name: 'Mechas', duration: 120, price: 85 },
    { id: '4', name: 'Balayage', duration: 150, price: 120 },
    { id: '5', name: 'Tratamiento', duration: 45, price: 35 },
  ]);

  const [stylists] = useState<Stylist[]>([
    { id: '1', name: 'Ana López' },
    { id: '2', name: 'Laura Martín' },
    { id: '3', name: 'Carmen Ruiz' },
  ]);

  const calculateTotalDuration = () => {
    return selectedServices.reduce((total, service) => total + service.duration, 0);
  };

  const calculateTotalPrice = () => {
    return selectedServices.reduce((total, service) => total + service.price, 0);
  };

  const calculateEndTime = () => {
    const [hours, minutes] = selectedTime.split(':').map(Number);
    const startTime = new Date(selectedDate);
    startTime.setHours(hours, minutes, 0, 0);
    
    const endTime = new Date(startTime.getTime() + calculateTotalDuration() * 60000);
    return endTime.toLocaleTimeString('es-ES', { hour: '2-digit', minute: '2-digit' });
  };

  const handleSave = async () => {
    if (!selectedClient) {
      Alert.alert('Error', 'Selecciona un cliente');
      return;
    }
    if (selectedServices.length === 0) {
      Alert.alert('Error', 'Selecciona al menos un servicio');
      return;
    }
    if (!selectedStylist) {
      Alert.alert('Error', 'Selecciona un estilista');
      return;
    }

    setLoading(true);
    try {
      // Simular guardado
      await new Promise(resolve => setTimeout(resolve, 1000));
      Alert.alert('Éxito', 'Cita creada correctamente');
    } catch (error) {
      Alert.alert('Error', 'No se pudo crear la cita');
    } finally {
      setLoading(false);
    }
  };

  const SelectionCard: React.FC<{
    title: string;
    value: string;
    onPress: () => void;
    icon: string;
  }> = ({ title, value, onPress, icon }) => (
    <TouchableOpacity style={styles.selectionCard} onPress={onPress}>
      <View style={styles.selectionLeft}>
        <Ionicons name={icon as any} size={24} color="#6366f1" />
        <View style={styles.selectionText}>
          <Text style={styles.selectionTitle}>{title}</Text>
          <Text style={styles.selectionValue}>{value}</Text>
        </View>
      </View>
      <Ionicons name="chevron-forward" size={20} color="#9ca3af" />
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#374151" />
        </TouchableOpacity>
        <Text style={styles.title}>Nueva Cita</Text>
        <TouchableOpacity 
          style={[styles.saveButton, loading && styles.saveButtonDisabled]}
          onPress={handleSave}
          disabled={loading}
        >
          <Text style={styles.saveButtonText}>
            {loading ? 'Guardando...' : 'Guardar'}
          </Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content}>
        {/* Client Selection */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Cliente</Text>
          <SelectionCard
            title="Cliente"
            value={selectedClient?.name || 'Seleccionar cliente'}
            onPress={() => Alert.alert('Seleccionar cliente', 'Funcionalidad próximamente')}
            icon="person-outline"
          />
        </View>

        {/* Services Selection */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Servicios</Text>
          {services.map((service) => (
            <TouchableOpacity
              key={service.id}
              style={[
                styles.serviceItem,
                selectedServices.find(s => s.id === service.id) && styles.serviceItemSelected,
              ]}
              onPress={() => {
                const isSelected = selectedServices.find(s => s.id === service.id);
                if (isSelected) {
                  setSelectedServices(selectedServices.filter(s => s.id !== service.id));
                } else {
                  setSelectedServices([...selectedServices, service]);
                }
              }}
            >
              <View style={styles.serviceLeft}>
                <View style={[
                  styles.checkbox,
                  selectedServices.find(s => s.id === service.id) && styles.checkboxSelected,
                ]}>
                  {selectedServices.find(s => s.id === service.id) && (
                    <Ionicons name="checkmark" size={16} color="white" />
                  )}
                </View>
                <View style={styles.serviceInfo}>
                  <Text style={styles.serviceName}>{service.name}</Text>
                  <Text style={styles.serviceDuration}>{service.duration} min</Text>
                </View>
              </View>
              <Text style={styles.servicePrice}>€{service.price}</Text>
            </TouchableOpacity>
          ))}
          
          {selectedServices.length > 0 && (
            <View style={styles.servicesTotal}>
              <Text style={styles.totalText}>
                Total: {calculateTotalDuration()} min - €{calculateTotalPrice()}
              </Text>
            </View>
          )}
        </View>

        {/* Stylist Selection */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Estilista</Text>
          <SelectionCard
            title="Estilista"
            value={selectedStylist?.name || 'Seleccionar estilista'}
            onPress={() => Alert.alert('Seleccionar estilista', 'Funcionalidad próximamente')}
            icon="color-palette-outline"
          />
        </View>

        {/* Date & Time Selection */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Fecha y Hora</Text>
          <SelectionCard
            title="Fecha"
            value={selectedDate.toLocaleDateString('es-ES', {
              weekday: 'long',
              day: 'numeric',
              month: 'long',
            })}
            onPress={() => Alert.alert('Seleccionar fecha', 'Funcionalidad próximamente')}
            icon="calendar-outline"
          />
          <SelectionCard
            title="Hora de inicio"
            value={selectedTime}
            onPress={() => Alert.alert('Seleccionar hora', 'Funcionalidad próximamente')}
            icon="time-outline"
          />
          {selectedServices.length > 0 && (
            <View style={styles.timeInfo}>
              <Text style={styles.timeInfoText}>
                Hora de finalización estimada: {calculateEndTime()}
              </Text>
            </View>
          )}
        </View>

        {/* Notes */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Notas</Text>
          <View style={styles.notesContainer}>
            <TextInput
              style={styles.notesInput}
              value={notes}
              onChangeText={setNotes}
              placeholder="Notas adicionales sobre la cita..."
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  backButton: {
    padding: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
  },
  saveButton: {
    backgroundColor: '#6366f1',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  saveButtonDisabled: {
    backgroundColor: '#9ca3af',
  },
  saveButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  content: {
    flex: 1,
  },
  section: {
    backgroundColor: 'white',
    marginBottom: 20,
    paddingHorizontal: 20,
    paddingVertical: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 16,
  },
  selectionCard: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 8,
    backgroundColor: '#f9fafb',
  },
  selectionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  selectionText: {
    marginLeft: 12,
  },
  selectionTitle: {
    fontSize: 14,
    color: '#6b7280',
  },
  selectionValue: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1f2937',
    marginTop: 2,
  },
  serviceItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 8,
    marginBottom: 8,
    backgroundColor: '#f9fafb',
  },
  serviceItemSelected: {
    borderColor: '#6366f1',
    backgroundColor: '#f0f9ff',
  },
  serviceLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: '#d1d5db',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  checkboxSelected: {
    backgroundColor: '#6366f1',
    borderColor: '#6366f1',
  },
  serviceInfo: {
    flex: 1,
  },
  serviceName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1f2937',
  },
  serviceDuration: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 2,
  },
  servicePrice: {
    fontSize: 16,
    fontWeight: '600',
    color: '#6366f1',
  },
  servicesTotal: {
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
  },
  totalText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
    textAlign: 'center',
  },
  timeInfo: {
    marginTop: 12,
    padding: 12,
    backgroundColor: '#f0f9ff',
    borderRadius: 8,
  },
  timeInfoText: {
    fontSize: 14,
    color: '#0369a1',
    textAlign: 'center',
  },
  notesContainer: {
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 8,
    backgroundColor: '#f9fafb',
  },
  notesInput: {
    padding: 16,
    fontSize: 16,
    color: '#1f2937',
    minHeight: 100,
    textAlignVertical: 'top',
  },
});
