import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Switch,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface PreferenceItem {
  id: string;
  title: string;
  subtitle?: string;
  type: 'switch' | 'select' | 'action';
  value?: boolean | string;
  options?: string[];
  icon: string;
}

export const PreferencesScreen: React.FC = () => {
  const [preferences, setPreferences] = useState<PreferenceItem[]>([
    {
      id: 'notifications_appointments',
      title: 'Notificaciones de citas',
      subtitle: 'Recibir alertas sobre citas próximas',
      type: 'switch',
      value: true,
      icon: 'notifications-outline',
    },
    {
      id: 'notifications_inventory',
      title: 'Alertas de inventario',
      subtitle: 'Notificar cuando el stock esté bajo',
      type: 'switch',
      value: true,
      icon: 'cube-outline',
    },
    {
      id: 'notifications_clients',
      title: 'Recordatorios de clientes',
      subtitle: 'Cumpleaños y citas de seguimiento',
      type: 'switch',
      value: false,
      icon: 'people-outline',
    },
    {
      id: 'preferred_brands',
      title: 'Marcas preferidas',
      subtitle: 'L\'Oréal Professional, Wella',
      type: 'action',
      icon: 'heart-outline',
    },
    {
      id: 'units',
      title: 'Unidades de medida',
      subtitle: 'Métrico (ml, g)',
      type: 'select',
      value: 'metric',
      options: ['metric', 'imperial'],
      icon: 'calculator-outline',
    },
    {
      id: 'language',
      title: 'Idioma',
      subtitle: 'Español',
      type: 'select',
      value: 'es',
      options: ['es', 'en', 'fr', 'it'],
      icon: 'language-outline',
    },
    {
      id: 'theme',
      title: 'Tema de la aplicación',
      subtitle: 'Claro',
      type: 'select',
      value: 'light',
      options: ['light', 'dark', 'auto'],
      icon: 'color-palette-outline',
    },
    {
      id: 'auto_backup',
      title: 'Respaldo automático',
      subtitle: 'Sincronizar datos en la nube',
      type: 'switch',
      value: true,
      icon: 'cloud-outline',
    },
    {
      id: 'analytics',
      title: 'Análisis de uso',
      subtitle: 'Ayudar a mejorar la aplicación',
      type: 'switch',
      value: false,
      icon: 'analytics-outline',
    },
  ]);

  const handleSwitchChange = (id: string, value: boolean) => {
    setPreferences(prev => 
      prev.map(pref => 
        pref.id === id ? { ...pref, value } : pref
      )
    );
  };

  const handleSelectPress = (item: PreferenceItem) => {
    if (item.type === 'select' && item.options) {
      const optionLabels = {
        metric: 'Métrico (ml, g)',
        imperial: 'Imperial (oz, lb)',
        es: 'Español',
        en: 'English',
        fr: 'Français',
        it: 'Italiano',
        light: 'Claro',
        dark: 'Oscuro',
        auto: 'Automático',
      };

      Alert.alert(
        item.title,
        'Selecciona una opción',
        item.options.map(option => ({
          text: optionLabels[option as keyof typeof optionLabels] || option,
          onPress: () => {
            setPreferences(prev => 
              prev.map(pref => 
                pref.id === item.id ? { ...pref, value: option } : pref
              )
            );
          },
        })).concat([{ text: 'Cancelar', style: 'cancel' }])
      );
    } else if (item.type === 'action') {
      handleActionPress(item);
    }
  };

  const handleActionPress = (item: PreferenceItem) => {
    switch (item.id) {
      case 'preferred_brands':
        Alert.alert('Marcas preferidas', 'Funcionalidad próximamente disponible');
        break;
      default:
        console.log('Action pressed:', item.id);
    }
  };

  const renderPreferenceItem = (item: PreferenceItem) => {
    const getSubtitleText = () => {
      if (item.type === 'select' && item.value) {
        const optionLabels = {
          metric: 'Métrico (ml, g)',
          imperial: 'Imperial (oz, lb)',
          es: 'Español',
          en: 'English',
          fr: 'Français',
          it: 'Italiano',
          light: 'Claro',
          dark: 'Oscuro',
          auto: 'Automático',
        };
        return optionLabels[item.value as keyof typeof optionLabels] || item.value;
      }
      return item.subtitle;
    };

    return (
      <TouchableOpacity
        key={item.id}
        style={styles.preferenceItem}
        onPress={() => item.type !== 'switch' && handleSelectPress(item)}
        disabled={item.type === 'switch'}
      >
        <View style={styles.preferenceLeft}>
          <Ionicons name={item.icon as any} size={24} color="#6366f1" />
          <View style={styles.preferenceText}>
            <Text style={styles.preferenceTitle}>{item.title}</Text>
            {getSubtitleText() && (
              <Text style={styles.preferenceSubtitle}>{getSubtitleText()}</Text>
            )}
          </View>
        </View>
        <View style={styles.preferenceRight}>
          {item.type === 'switch' ? (
            <Switch
              value={item.value as boolean}
              onValueChange={(value) => handleSwitchChange(item.id, value)}
              trackColor={{ false: '#f3f4f6', true: '#dbeafe' }}
              thumbColor={item.value ? '#6366f1' : '#9ca3af'}
            />
          ) : (
            <Ionicons name="chevron-forward" size={20} color="#9ca3af" />
          )}
        </View>
      </TouchableOpacity>
    );
  };

  const handleSavePreferences = () => {
    Alert.alert('Éxito', 'Preferencias guardadas correctamente');
  };

  const handleResetPreferences = () => {
    Alert.alert(
      'Restablecer preferencias',
      '¿Estás seguro de que quieres restablecer todas las preferencias a sus valores por defecto?',
      [
        { text: 'Cancelar', style: 'cancel' },
        { 
          text: 'Restablecer', 
          style: 'destructive',
          onPress: () => {
            // Reset to default values
            Alert.alert('Éxito', 'Preferencias restablecidas');
          }
        },
      ]
    );
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#374151" />
        </TouchableOpacity>
        <Text style={styles.title}>Preferencias</Text>
        <TouchableOpacity style={styles.saveButton} onPress={handleSavePreferences}>
          <Text style={styles.saveButtonText}>Guardar</Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content}>
        {/* Notifications Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Notificaciones</Text>
          {preferences
            .filter(pref => pref.id.startsWith('notifications_'))
            .map(renderPreferenceItem)}
        </View>

        {/* App Preferences */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Preferencias de la App</Text>
          {preferences
            .filter(pref => ['preferred_brands', 'units', 'language', 'theme'].includes(pref.id))
            .map(renderPreferenceItem)}
        </View>

        {/* Data & Privacy */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Datos y Privacidad</Text>
          {preferences
            .filter(pref => ['auto_backup', 'analytics'].includes(pref.id))
            .map(renderPreferenceItem)}
        </View>

        {/* Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Acciones</Text>
          
          <TouchableOpacity style={styles.actionButton}>
            <Ionicons name="download-outline" size={24} color="#6366f1" />
            <View style={styles.actionText}>
              <Text style={styles.actionTitle}>Exportar datos</Text>
              <Text style={styles.actionSubtitle}>Descargar una copia de tus datos</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color="#9ca3af" />
          </TouchableOpacity>

          <TouchableOpacity style={styles.actionButton}>
            <Ionicons name="trash-outline" size={24} color="#6366f1" />
            <View style={styles.actionText}>
              <Text style={styles.actionTitle}>Limpiar caché</Text>
              <Text style={styles.actionSubtitle}>Liberar espacio de almacenamiento</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color="#9ca3af" />
          </TouchableOpacity>

          <TouchableOpacity style={styles.dangerButton} onPress={handleResetPreferences}>
            <Ionicons name="refresh-outline" size={24} color="#ef4444" />
            <View style={styles.actionText}>
              <Text style={[styles.actionTitle, { color: '#ef4444' }]}>
                Restablecer preferencias
              </Text>
              <Text style={styles.actionSubtitle}>
                Volver a los valores por defecto
              </Text>
            </View>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  backButton: {
    padding: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
  },
  saveButton: {
    backgroundColor: '#6366f1',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  saveButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  content: {
    flex: 1,
  },
  section: {
    backgroundColor: 'white',
    marginBottom: 20,
    paddingVertical: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  preferenceItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  preferenceLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  preferenceText: {
    marginLeft: 16,
    flex: 1,
  },
  preferenceTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1f2937',
  },
  preferenceSubtitle: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 2,
  },
  preferenceRight: {
    marginLeft: 16,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  dangerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  actionText: {
    marginLeft: 16,
    flex: 1,
  },
  actionTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1f2937',
  },
  actionSubtitle: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 2,
  },
});
