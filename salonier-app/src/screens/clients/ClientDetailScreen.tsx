import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Linking,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Client } from '../../types';

interface ClientDetailScreenProps {
  route: {
    params: {
      clientId: string;
    };
  };
}

export const ClientDetailScreen: React.FC<ClientDetailScreenProps> = ({ route }) => {
  const { clientId } = route.params;
  const [client, setClient] = useState<Client | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadClientDetails();
  }, [clientId]);

  const loadClientDetails = async () => {
    try {
      // Simular carga de datos del cliente
      const mockClient: Client = {
        id: clientId,
        full_name: '<PERSON>',
        email: '<EMAIL>',
        phone: '+34 666 123 456',
        birthday: '1985-03-15',
        notes: 'Alérgica al amoníaco. Prefiere tonos cálidos. Cliente desde 2020.',
        salon_id: '1',
        created_at: '2020-03-15T10:00:00Z',
        updated_at: '2024-06-17T14:30:00Z',
      };
      setClient(mockClient);
    } catch (error) {
      console.error('Error loading client details:', error);
      Alert.alert('Error', 'No se pudieron cargar los detalles del cliente');
    } finally {
      setLoading(false);
    }
  };

  const handleCall = () => {
    if (client?.phone) {
      Linking.openURL(`tel:${client.phone}`);
    }
  };

  const handleEmail = () => {
    if (client?.email) {
      Linking.openURL(`mailto:${client.email}`);
    }
  };

  const handleWhatsApp = () => {
    if (client?.phone) {
      const phoneNumber = client.phone.replace(/\s+/g, '').replace('+', '');
      Linking.openURL(`whatsapp://send?phone=${phoneNumber}`);
    }
  };

  const handleNewAppointment = () => {
    Alert.alert('Nueva cita', 'Funcionalidad próximamente disponible');
  };

  const handleNewConsultation = () => {
    Alert.alert('Nueva consulta', 'Funcionalidad próximamente disponible');
  };

  const calculateAge = (birthday: string) => {
    const today = new Date();
    const birthDate = new Date(birthday);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    
    return age;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  if (loading) {
    return (
      <View style={[styles.container, styles.centered]}>
        <Text>Cargando...</Text>
      </View>
    );
  }

  if (!client) {
    return (
      <View style={[styles.container, styles.centered]}>
        <Text>Cliente no encontrado</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#374151" />
        </TouchableOpacity>
        <Text style={styles.title}>Detalles del Cliente</Text>
        <TouchableOpacity style={styles.editButton}>
          <Ionicons name="create-outline" size={24} color="#6366f1" />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content}>
        {/* Client Info Card */}
        <View style={styles.clientCard}>
          <View style={styles.clientAvatar}>
            <Text style={styles.clientInitials}>
              {client.full_name.split(' ').map(n => n[0]).join('').toUpperCase()}
            </Text>
          </View>
          <Text style={styles.clientName}>{client.full_name}</Text>
          {client.birthday && (
            <Text style={styles.clientAge}>
              {calculateAge(client.birthday)} años
            </Text>
          )}
          
          {/* Contact Actions */}
          <View style={styles.contactActions}>
            {client.phone && (
              <TouchableOpacity style={styles.contactButton} onPress={handleCall}>
                <Ionicons name="call" size={20} color="white" />
              </TouchableOpacity>
            )}
            {client.email && (
              <TouchableOpacity style={styles.contactButton} onPress={handleEmail}>
                <Ionicons name="mail" size={20} color="white" />
              </TouchableOpacity>
            )}
            {client.phone && (
              <TouchableOpacity style={styles.contactButton} onPress={handleWhatsApp}>
                <Ionicons name="logo-whatsapp" size={20} color="white" />
              </TouchableOpacity>
            )}
          </View>
        </View>

        {/* Quick Actions */}
        <View style={styles.quickActions}>
          <TouchableOpacity style={styles.actionButton} onPress={handleNewAppointment}>
            <Ionicons name="calendar-outline" size={24} color="#6366f1" />
            <Text style={styles.actionButtonText}>Nueva Cita</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.actionButton} onPress={handleNewConsultation}>
            <Ionicons name="color-palette-outline" size={24} color="#6366f1" />
            <Text style={styles.actionButtonText}>Consulta IA</Text>
          </TouchableOpacity>
        </View>

        {/* Contact Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Información de Contacto</Text>
          <View style={styles.infoContainer}>
            {client.email && (
              <View style={styles.infoItem}>
                <Ionicons name="mail-outline" size={20} color="#6b7280" />
                <Text style={styles.infoText}>{client.email}</Text>
              </View>
            )}
            {client.phone && (
              <View style={styles.infoItem}>
                <Ionicons name="call-outline" size={20} color="#6b7280" />
                <Text style={styles.infoText}>{client.phone}</Text>
              </View>
            )}
            {client.birthday && (
              <View style={styles.infoItem}>
                <Ionicons name="gift-outline" size={20} color="#6b7280" />
                <Text style={styles.infoText}>
                  {formatDate(client.birthday)} ({calculateAge(client.birthday)} años)
                </Text>
              </View>
            )}
          </View>
        </View>

        {/* Notes */}
        {client.notes && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Notas</Text>
            <View style={styles.notesContainer}>
              <Text style={styles.notesText}>{client.notes}</Text>
            </View>
          </View>
        )}

        {/* Statistics */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Estadísticas</Text>
          <View style={styles.statsContainer}>
            <View style={styles.statItem}>
              <Text style={styles.statNumber}>12</Text>
              <Text style={styles.statLabel}>Citas Totales</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statNumber}>€850</Text>
              <Text style={styles.statLabel}>Gasto Total</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statNumber}>€71</Text>
              <Text style={styles.statLabel}>Ticket Promedio</Text>
            </View>
          </View>
        </View>

        {/* Recent History */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Historial Reciente</Text>
            <TouchableOpacity>
              <Text style={styles.seeAllText}>Ver todo</Text>
            </TouchableOpacity>
          </View>
          
          <View style={styles.historyContainer}>
            <View style={styles.historyItem}>
              <View style={styles.historyIcon}>
                <Ionicons name="color-palette" size={20} color="#6366f1" />
              </View>
              <View style={styles.historyContent}>
                <Text style={styles.historyTitle}>Coloración + Corte</Text>
                <Text style={styles.historyDate}>15 de mayo, 2024</Text>
                <Text style={styles.historyPrice}>€85</Text>
              </View>
            </View>

            <View style={styles.historyItem}>
              <View style={styles.historyIcon}>
                <Ionicons name="cut" size={20} color="#10b981" />
              </View>
              <View style={styles.historyContent}>
                <Text style={styles.historyTitle}>Retoque de Raíces</Text>
                <Text style={styles.historyDate}>20 de abril, 2024</Text>
                <Text style={styles.historyPrice}>€45</Text>
              </View>
            </View>

            <View style={styles.historyItem}>
              <View style={styles.historyIcon}>
                <Ionicons name="sparkles" size={20} color="#f59e0b" />
              </View>
              <View style={styles.historyContent}>
                <Text style={styles.historyTitle}>Mechas + Tratamiento</Text>
                <Text style={styles.historyDate}>10 de marzo, 2024</Text>
                <Text style={styles.historyPrice}>€120</Text>
              </View>
            </View>
          </View>
        </View>

        {/* Client Since */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Información Adicional</Text>
          <View style={styles.infoContainer}>
            <View style={styles.infoItem}>
              <Ionicons name="calendar-outline" size={20} color="#6b7280" />
              <Text style={styles.infoText}>
                Cliente desde {formatDate(client.created_at)}
              </Text>
            </View>
            <View style={styles.infoItem}>
              <Ionicons name="time-outline" size={20} color="#6b7280" />
              <Text style={styles.infoText}>
                Última actualización: {formatDate(client.updated_at)}
              </Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  backButton: {
    padding: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
  },
  editButton: {
    padding: 8,
  },
  content: {
    flex: 1,
  },
  clientCard: {
    backgroundColor: 'white',
    alignItems: 'center',
    paddingVertical: 32,
    marginBottom: 20,
  },
  clientAvatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#6366f1',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  clientInitials: {
    color: 'white',
    fontSize: 24,
    fontWeight: 'bold',
  },
  clientName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 4,
  },
  clientAge: {
    fontSize: 16,
    color: '#6b7280',
    marginBottom: 20,
  },
  contactActions: {
    flexDirection: 'row',
    gap: 12,
  },
  contactButton: {
    backgroundColor: '#6366f1',
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
  },
  quickActions: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    gap: 12,
    marginBottom: 20,
  },
  actionButton: {
    flex: 1,
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  actionButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#374151',
    marginTop: 8,
  },
  section: {
    backgroundColor: 'white',
    marginBottom: 20,
    paddingHorizontal: 20,
    paddingVertical: 20,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 16,
  },
  seeAllText: {
    fontSize: 14,
    color: '#6366f1',
    fontWeight: '600',
  },
  infoContainer: {
    gap: 16,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  infoText: {
    fontSize: 16,
    color: '#374151',
  },
  notesContainer: {
    backgroundColor: '#f9fafb',
    borderRadius: 8,
    padding: 16,
  },
  notesText: {
    fontSize: 16,
    color: '#374151',
    lineHeight: 24,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  statLabel: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 4,
  },
  historyContainer: {
    gap: 16,
  },
  historyItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  historyIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f3f4f6',
    justifyContent: 'center',
    alignItems: 'center',
  },
  historyContent: {
    flex: 1,
  },
  historyTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
  },
  historyDate: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 2,
  },
  historyPrice: {
    fontSize: 16,
    fontWeight: '600',
    color: '#6366f1',
    marginTop: 4,
  },
});
