import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Client } from '../../types';

interface ClientFormScreenProps {
  route: {
    params?: {
      clientId?: string;
    };
  };
}

export const ClientFormScreen: React.FC<ClientFormScreenProps> = ({ route }) => {
  const clientId = route.params?.clientId;
  const isEditing = !!clientId;
  
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    full_name: '',
    email: '',
    phone: '',
    birthday: '',
    notes: '',
  });

  useEffect(() => {
    if (isEditing) {
      loadClientData();
    }
  }, [clientId]);

  const loadClientData = async () => {
    try {
      // Simular carga de datos del cliente para edición
      const mockClient = {
        full_name: '<PERSON>',
        email: '<EMAIL>',
        phone: '+34 666 123 456',
        birthday: '1985-03-15',
        notes: 'Alérgica al amoníaco. Prefiere tonos cálidos.',
      };
      setFormData(mockClient);
    } catch (error) {
      console.error('Error loading client data:', error);
      Alert.alert('Error', 'No se pudieron cargar los datos del cliente');
    }
  };

  const handleSave = async () => {
    // Validaciones
    if (!formData.full_name.trim()) {
      Alert.alert('Error', 'El nombre es obligatorio');
      return;
    }

    if (formData.email && !isValidEmail(formData.email)) {
      Alert.alert('Error', 'El correo electrónico no es válido');
      return;
    }

    setLoading(true);
    try {
      // Simular guardado
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      Alert.alert(
        'Éxito',
        `Cliente ${isEditing ? 'actualizado' : 'creado'} correctamente`,
        [{ text: 'OK', onPress: () => console.log('Navigate back') }]
      );
    } catch (error) {
      Alert.alert('Error', `No se pudo ${isEditing ? 'actualizar' : 'crear'} el cliente`);
    } finally {
      setLoading(false);
    }
  };

  const isValidEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const formatPhoneNumber = (phone: string) => {
    // Formatear número de teléfono español
    const cleaned = phone.replace(/\D/g, '');
    if (cleaned.length === 9) {
      return `+34 ${cleaned.slice(0, 3)} ${cleaned.slice(3, 6)} ${cleaned.slice(6)}`;
    }
    return phone;
  };

  const handlePhoneChange = (text: string) => {
    const formatted = formatPhoneNumber(text);
    setFormData({ ...formData, phone: formatted });
  };

  const formatBirthday = (date: string) => {
    // Formatear fecha DD/MM/YYYY
    const cleaned = date.replace(/\D/g, '');
    if (cleaned.length >= 2) {
      let formatted = cleaned.slice(0, 2);
      if (cleaned.length >= 4) {
        formatted += '/' + cleaned.slice(2, 4);
        if (cleaned.length >= 6) {
          formatted += '/' + cleaned.slice(4, 8);
        }
      }
      return formatted;
    }
    return cleaned;
  };

  const handleBirthdayChange = (text: string) => {
    const formatted = formatBirthday(text);
    setFormData({ ...formData, birthday: formatted });
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#374151" />
        </TouchableOpacity>
        <Text style={styles.title}>
          {isEditing ? 'Editar Cliente' : 'Nuevo Cliente'}
        </Text>
        <TouchableOpacity 
          style={[styles.saveButton, loading && styles.saveButtonDisabled]}
          onPress={handleSave}
          disabled={loading}
        >
          <Text style={styles.saveButtonText}>
            {loading ? 'Guardando...' : 'Guardar'}
          </Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content}>
        {/* Personal Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Información Personal</Text>
          
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Nombre completo *</Text>
            <View style={styles.inputContainer}>
              <Ionicons name="person-outline" size={20} color="#9ca3af" style={styles.inputIcon} />
              <TextInput
                style={styles.input}
                value={formData.full_name}
                onChangeText={(text) => setFormData({ ...formData, full_name: text })}
                placeholder="Ej: María García López"
                autoCapitalize="words"
                autoCorrect={false}
              />
            </View>
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Fecha de nacimiento</Text>
            <View style={styles.inputContainer}>
              <Ionicons name="calendar-outline" size={20} color="#9ca3af" style={styles.inputIcon} />
              <TextInput
                style={styles.input}
                value={formData.birthday}
                onChangeText={handleBirthdayChange}
                placeholder="DD/MM/YYYY"
                keyboardType="numeric"
                maxLength={10}
              />
            </View>
            <Text style={styles.inputHint}>
              Formato: DD/MM/YYYY (ej: 15/03/1985)
            </Text>
          </View>
        </View>

        {/* Contact Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Información de Contacto</Text>
          
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Correo electrónico</Text>
            <View style={styles.inputContainer}>
              <Ionicons name="mail-outline" size={20} color="#9ca3af" style={styles.inputIcon} />
              <TextInput
                style={styles.input}
                value={formData.email}
                onChangeText={(text) => setFormData({ ...formData, email: text })}
                placeholder="<EMAIL>"
                keyboardType="email-address"
                autoCapitalize="none"
                autoCorrect={false}
              />
            </View>
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Teléfono</Text>
            <View style={styles.inputContainer}>
              <Ionicons name="call-outline" size={20} color="#9ca3af" style={styles.inputIcon} />
              <TextInput
                style={styles.input}
                value={formData.phone}
                onChangeText={handlePhoneChange}
                placeholder="+34 666 123 456"
                keyboardType="phone-pad"
              />
            </View>
            <Text style={styles.inputHint}>
              Se formateará automáticamente para España
            </Text>
          </View>
        </View>

        {/* Additional Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Información Adicional</Text>
          
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Notas</Text>
            <View style={[styles.inputContainer, styles.textAreaContainer]}>
              <Ionicons name="document-text-outline" size={20} color="#9ca3af" style={styles.inputIcon} />
              <TextInput
                style={[styles.input, styles.textArea]}
                value={formData.notes}
                onChangeText={(text) => setFormData({ ...formData, notes: text })}
                placeholder="Alergias, preferencias, observaciones..."
                multiline
                numberOfLines={4}
                textAlignVertical="top"
              />
            </View>
            <Text style={styles.inputHint}>
              Incluye información importante como alergias, preferencias de color, etc.
            </Text>
          </View>
        </View>

        {/* Quick Notes Templates */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Plantillas Rápidas</Text>
          <Text style={styles.sectionSubtitle}>
            Toca para agregar a las notas
          </Text>
          
          <View style={styles.templatesContainer}>
            {[
              'Alérgica al amoníaco',
              'Prefiere tonos cálidos',
              'Prefiere tonos fríos',
              'Cabello sensible',
              'Cliente VIP',
              'Embarazada',
              'Cabello virgen',
              'Mechas previas',
            ].map((template, index) => (
              <TouchableOpacity
                key={index}
                style={styles.templateChip}
                onPress={() => {
                  const currentNotes = formData.notes;
                  const newNotes = currentNotes 
                    ? `${currentNotes}\n• ${template}`
                    : `• ${template}`;
                  setFormData({ ...formData, notes: newNotes });
                }}
              >
                <Text style={styles.templateText}>{template}</Text>
                <Ionicons name="add" size={16} color="#6366f1" />
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Delete Button (only when editing) */}
        {isEditing && (
          <View style={styles.section}>
            <TouchableOpacity style={styles.deleteButton}>
              <Ionicons name="trash-outline" size={20} color="#ef4444" />
              <Text style={styles.deleteButtonText}>Eliminar Cliente</Text>
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  backButton: {
    padding: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
  },
  saveButton: {
    backgroundColor: '#6366f1',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  saveButtonDisabled: {
    backgroundColor: '#9ca3af',
  },
  saveButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  content: {
    flex: 1,
  },
  section: {
    backgroundColor: 'white',
    marginBottom: 20,
    paddingHorizontal: 20,
    paddingVertical: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 4,
  },
  sectionSubtitle: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 16,
  },
  inputGroup: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 8,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 8,
    paddingHorizontal: 12,
    backgroundColor: '#f9fafb',
    minHeight: 48,
  },
  textAreaContainer: {
    alignItems: 'flex-start',
    paddingVertical: 12,
  },
  inputIcon: {
    marginRight: 12,
  },
  input: {
    flex: 1,
    fontSize: 16,
    color: '#1f2937',
    paddingVertical: 12,
  },
  textArea: {
    minHeight: 80,
    textAlignVertical: 'top',
  },
  inputHint: {
    fontSize: 12,
    color: '#6b7280',
    marginTop: 4,
  },
  templatesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  templateChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f0f9ff',
    borderWidth: 1,
    borderColor: '#bae6fd',
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 6,
    gap: 4,
  },
  templateText: {
    fontSize: 14,
    color: '#0369a1',
  },
  deleteButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#fecaca',
    borderRadius: 8,
    paddingVertical: 12,
    backgroundColor: '#fef2f2',
    gap: 8,
  },
  deleteButtonText: {
    color: '#ef4444',
    fontSize: 16,
    fontWeight: '600',
  },
});
