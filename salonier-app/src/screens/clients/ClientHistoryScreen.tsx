import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  FlatList,
  Image,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface HistoryItem {
  id: string;
  type: 'appointment' | 'consultation' | 'note';
  title: string;
  date: string;
  price?: number;
  services?: string[];
  notes?: string;
  images?: string[];
  stylist?: string;
  status: 'completed' | 'cancelled' | 'scheduled';
}

interface ClientHistoryScreenProps {
  route: {
    params: {
      clientId: string;
      clientName: string;
    };
  };
}

export const ClientHistoryScreen: React.FC<ClientHistoryScreenProps> = ({ route }) => {
  const { clientId, clientName } = route.params;
  const [history, setHistory] = useState<HistoryItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'appointments' | 'consultations' | 'notes'>('all');

  useEffect(() => {
    loadClientHistory();
  }, [clientId]);

  const loadClientHistory = async () => {
    try {
      // Simular carga de historial
      const mockHistory: HistoryItem[] = [
        {
          id: '1',
          type: 'appointment',
          title: 'Coloración + Corte',
          date: '2024-05-15T10:00:00Z',
          price: 85,
          services: ['Coloración completa', 'Corte y peinado'],
          notes: 'Cliente muy satisfecha con el resultado. Tono perfecto.',
          stylist: 'Ana García',
          status: 'completed',
        },
        {
          id: '2',
          type: 'consultation',
          title: 'Consulta de Coloración IA',
          date: '2024-05-14T16:30:00Z',
          notes: 'Análisis capilar completo. Recomendación: rubio miel con reflejos dorados.',
          images: ['consultation1.jpg', 'consultation2.jpg'],
          stylist: 'Ana García',
          status: 'completed',
        },
        {
          id: '3',
          type: 'appointment',
          title: 'Retoque de Raíces',
          date: '2024-04-20T14:00:00Z',
          price: 45,
          services: ['Retoque de raíces'],
          notes: 'Mantenimiento regular. Sin cambios.',
          stylist: 'Ana García',
          status: 'completed',
        },
        {
          id: '4',
          type: 'note',
          title: 'Nota del Estilista',
          date: '2024-04-20T14:30:00Z',
          notes: 'Cliente menciona que quiere probar un cambio más drástico en la próxima cita.',
          stylist: 'Ana García',
          status: 'completed',
        },
        {
          id: '5',
          type: 'appointment',
          title: 'Mechas + Tratamiento',
          date: '2024-03-10T11:00:00Z',
          price: 120,
          services: ['Mechas', 'Tratamiento reparador'],
          notes: 'Primera vez con mechas. Resultado excelente.',
          stylist: 'Ana García',
          status: 'completed',
        },
        {
          id: '6',
          type: 'appointment',
          title: 'Cita Próxima',
          date: '2024-06-20T10:00:00Z',
          price: 95,
          services: ['Coloración', 'Corte'],
          notes: 'Cita programada para cambio de look.',
          stylist: 'Ana García',
          status: 'scheduled',
        },
      ];
      setHistory(mockHistory);
    } catch (error) {
      console.error('Error loading client history:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredHistory = history.filter(item => {
    if (filter === 'all') return true;
    if (filter === 'appointments') return item.type === 'appointment';
    if (filter === 'consultations') return item.type === 'consultation';
    if (filter === 'notes') return item.type === 'note';
    return true;
  });

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const isToday = date.toDateString() === now.toDateString();
    const isFuture = date > now;
    
    if (isToday) {
      return `Hoy, ${date.toLocaleTimeString('es-ES', { hour: '2-digit', minute: '2-digit' })}`;
    } else if (isFuture) {
      return `${date.toLocaleDateString('es-ES', { 
        weekday: 'long', 
        day: 'numeric', 
        month: 'long' 
      })}, ${date.toLocaleTimeString('es-ES', { hour: '2-digit', minute: '2-digit' })}`;
    } else {
      return date.toLocaleDateString('es-ES', {
        day: 'numeric',
        month: 'long',
        year: 'numeric',
      });
    }
  };

  const getItemIcon = (type: string, status: string) => {
    if (type === 'appointment') {
      return status === 'scheduled' ? 'calendar-outline' : 'calendar';
    } else if (type === 'consultation') {
      return 'color-palette';
    } else {
      return 'document-text';
    }
  };

  const getItemColor = (type: string, status: string) => {
    if (status === 'scheduled') return '#f59e0b';
    if (type === 'appointment') return '#6366f1';
    if (type === 'consultation') return '#10b981';
    return '#6b7280';
  };

  const renderHistoryItem = ({ item }: { item: HistoryItem }) => (
    <TouchableOpacity style={styles.historyItem}>
      <View style={[styles.itemIcon, { backgroundColor: getItemColor(item.type, item.status) }]}>
        <Ionicons 
          name={getItemIcon(item.type, item.status) as any} 
          size={20} 
          color="white" 
        />
      </View>
      
      <View style={styles.itemContent}>
        <View style={styles.itemHeader}>
          <Text style={styles.itemTitle}>{item.title}</Text>
          {item.price && (
            <Text style={styles.itemPrice}>€{item.price}</Text>
          )}
        </View>
        
        <Text style={styles.itemDate}>{formatDate(item.date)}</Text>
        
        {item.services && (
          <View style={styles.servicesContainer}>
            {item.services.map((service, index) => (
              <View key={index} style={styles.serviceChip}>
                <Text style={styles.serviceText}>{service}</Text>
              </View>
            ))}
          </View>
        )}
        
        {item.notes && (
          <Text style={styles.itemNotes} numberOfLines={2}>
            {item.notes}
          </Text>
        )}
        
        {item.stylist && (
          <View style={styles.stylistContainer}>
            <Ionicons name="person-outline" size={14} color="#6b7280" />
            <Text style={styles.stylistText}>{item.stylist}</Text>
          </View>
        )}
        
        {item.images && item.images.length > 0 && (
          <View style={styles.imagesContainer}>
            <Ionicons name="image-outline" size={14} color="#6b7280" />
            <Text style={styles.imagesText}>
              {item.images.length} imagen{item.images.length > 1 ? 'es' : ''}
            </Text>
          </View>
        )}
      </View>
      
      <Ionicons name="chevron-forward" size={20} color="#9ca3af" />
    </TouchableOpacity>
  );

  const FilterButton: React.FC<{
    title: string;
    value: string;
    count: number;
  }> = ({ title, value, count }) => (
    <TouchableOpacity
      style={[
        styles.filterButton,
        filter === value && styles.filterButtonActive,
      ]}
      onPress={() => setFilter(value as any)}
    >
      <Text
        style={[
          styles.filterButtonText,
          filter === value && styles.filterButtonTextActive,
        ]}
      >
        {title} ({count})
      </Text>
    </TouchableOpacity>
  );

  const getFilterCounts = () => {
    return {
      all: history.length,
      appointments: history.filter(item => item.type === 'appointment').length,
      consultations: history.filter(item => item.type === 'consultation').length,
      notes: history.filter(item => item.type === 'note').length,
    };
  };

  const counts = getFilterCounts();

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#374151" />
        </TouchableOpacity>
        <View style={styles.headerContent}>
          <Text style={styles.title}>Historial</Text>
          <Text style={styles.subtitle}>{clientName}</Text>
        </View>
        <TouchableOpacity style={styles.searchButton}>
          <Ionicons name="search-outline" size={24} color="#374151" />
        </TouchableOpacity>
      </View>

      {/* Filters */}
      <View style={styles.filtersContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View style={styles.filtersContent}>
            <FilterButton title="Todo" value="all" count={counts.all} />
            <FilterButton title="Citas" value="appointments" count={counts.appointments} />
            <FilterButton title="Consultas" value="consultations" count={counts.consultations} />
            <FilterButton title="Notas" value="notes" count={counts.notes} />
          </View>
        </ScrollView>
      </View>

      {/* Summary Stats */}
      <View style={styles.summaryContainer}>
        <View style={styles.summaryItem}>
          <Text style={styles.summaryNumber}>{counts.appointments}</Text>
          <Text style={styles.summaryLabel}>Citas</Text>
        </View>
        <View style={styles.summaryItem}>
          <Text style={styles.summaryNumber}>
            €{history
              .filter(item => item.price)
              .reduce((sum, item) => sum + (item.price || 0), 0)}
          </Text>
          <Text style={styles.summaryLabel}>Total Gastado</Text>
        </View>
        <View style={styles.summaryItem}>
          <Text style={styles.summaryNumber}>
            {Math.round(
              history
                .filter(item => item.price)
                .reduce((sum, item) => sum + (item.price || 0), 0) / 
              Math.max(counts.appointments, 1)
            )}€
          </Text>
          <Text style={styles.summaryLabel}>Ticket Promedio</Text>
        </View>
      </View>

      {/* History List */}
      <FlatList
        data={filteredHistory}
        renderItem={renderHistoryItem}
        keyExtractor={(item) => item.id}
        style={styles.historyList}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="document-outline" size={64} color="#d1d5db" />
            <Text style={styles.emptyTitle}>No hay historial</Text>
            <Text style={styles.emptySubtitle}>
              {filter === 'all' 
                ? 'Este cliente no tiene historial aún'
                : `No hay ${filter} registradas`}
            </Text>
          </View>
        }
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  backButton: {
    padding: 8,
    marginRight: 12,
  },
  headerContent: {
    flex: 1,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
  },
  subtitle: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 2,
  },
  searchButton: {
    padding: 8,
  },
  filtersContainer: {
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  filtersContent: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
    gap: 12,
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#f3f4f6',
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  filterButtonActive: {
    backgroundColor: '#6366f1',
    borderColor: '#6366f1',
  },
  filterButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6b7280',
  },
  filterButtonTextActive: {
    color: 'white',
  },
  summaryContainer: {
    flexDirection: 'row',
    backgroundColor: 'white',
    paddingVertical: 20,
    marginBottom: 20,
  },
  summaryItem: {
    flex: 1,
    alignItems: 'center',
  },
  summaryNumber: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  summaryLabel: {
    fontSize: 12,
    color: '#6b7280',
    marginTop: 4,
  },
  historyList: {
    flex: 1,
    paddingHorizontal: 20,
  },
  historyItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: 'white',
    padding: 16,
    marginBottom: 12,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  itemIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  itemContent: {
    flex: 1,
  },
  itemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 4,
  },
  itemTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
    flex: 1,
  },
  itemPrice: {
    fontSize: 16,
    fontWeight: '600',
    color: '#6366f1',
  },
  itemDate: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 8,
  },
  servicesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 6,
    marginBottom: 8,
  },
  serviceChip: {
    backgroundColor: '#f0f9ff',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  serviceText: {
    fontSize: 12,
    color: '#0369a1',
  },
  itemNotes: {
    fontSize: 14,
    color: '#374151',
    lineHeight: 20,
    marginBottom: 8,
  },
  stylistContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    marginBottom: 4,
  },
  stylistText: {
    fontSize: 12,
    color: '#6b7280',
  },
  imagesContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  imagesText: {
    fontSize: 12,
    color: '#6b7280',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#374151',
    marginTop: 16,
  },
  emptySubtitle: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 8,
    textAlign: 'center',
  },
});
