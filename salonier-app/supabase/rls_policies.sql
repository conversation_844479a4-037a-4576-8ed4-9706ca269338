-- Políticas de Row Level Security (RLS) para Salonier
-- Estas políticas garantizan que los usuarios solo accedan a datos autorizados

-- Habilitar RLS en todas las tablas
ALTER TABLE salons ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE clients ENABLE ROW LEVEL SECURITY;
ALTER TABLE services ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE appointments ENABLE ROW LEVEL SECURITY;
ALTER TABLE hair_diagnoses ENABLE ROW LEVEL SECURITY;
ALTER TABLE diagnosis_images ENABLE ROW LEVEL SECURITY;
ALTER TABLE color_formulations ENABLE ROW LEVEL SECURITY;
ALTER TABLE brands ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_lines ENABLE ROW LEVEL SECURITY;
ALTER TABLE inventory_movements ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;

-- <PERSON>ción helper para obtener el salon_id del usuario actual
CREATE OR REPLACE FUNCTION get_user_salon_id()
RETURNS UUID AS $$
BEGIN
    RETURN (
        SELECT salon_id 
        FROM users 
        WHERE id = auth.uid()
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Función helper para verificar si el usuario es admin del salón
CREATE OR REPLACE FUNCTION is_salon_admin()
RETURNS BOOLEAN AS $$
BEGIN
    RETURN (
        SELECT role = 'admin' 
        FROM users 
        WHERE id = auth.uid()
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Políticas para la tabla SALONS
-- Los usuarios pueden ver su propio salón
CREATE POLICY "Users can view their own salon" ON salons
    FOR SELECT USING (
        id = get_user_salon_id() OR 
        owner_id = auth.uid()
    );

-- Solo el propietario puede actualizar el salón
CREATE POLICY "Salon owners can update their salon" ON salons
    FOR UPDATE USING (owner_id = auth.uid());

-- Solo usuarios autenticados pueden crear salones
CREATE POLICY "Authenticated users can create salons" ON salons
    FOR INSERT WITH CHECK (auth.uid() IS NOT NULL);

-- Políticas para la tabla USERS
-- Los usuarios pueden ver su propio perfil y otros usuarios del mismo salón
CREATE POLICY "Users can view profiles in their salon" ON users
    FOR SELECT USING (
        id = auth.uid() OR 
        salon_id = get_user_salon_id()
    );

-- Los usuarios pueden actualizar su propio perfil
CREATE POLICY "Users can update their own profile" ON users
    FOR UPDATE USING (id = auth.uid());

-- Los admins pueden actualizar perfiles de su salón
CREATE POLICY "Admins can update salon user profiles" ON users
    FOR UPDATE USING (
        is_salon_admin() AND 
        salon_id = get_user_salon_id()
    );

-- Los usuarios pueden insertar su propio perfil
CREATE POLICY "Users can insert their own profile" ON users
    FOR INSERT WITH CHECK (id = auth.uid());

-- Políticas para la tabla CLIENTS
-- Los usuarios pueden ver clientes de su salón
CREATE POLICY "Users can view salon clients" ON clients
    FOR SELECT USING (salon_id = get_user_salon_id());

-- Los usuarios pueden crear clientes en su salón
CREATE POLICY "Users can create salon clients" ON clients
    FOR INSERT WITH CHECK (salon_id = get_user_salon_id());

-- Los usuarios pueden actualizar clientes de su salón
CREATE POLICY "Users can update salon clients" ON clients
    FOR UPDATE USING (salon_id = get_user_salon_id());

-- Solo admins pueden eliminar clientes
CREATE POLICY "Admins can delete salon clients" ON clients
    FOR DELETE USING (
        is_salon_admin() AND 
        salon_id = get_user_salon_id()
    );

-- Políticas para la tabla SERVICES
-- Los usuarios pueden ver servicios de su salón
CREATE POLICY "Users can view salon services" ON services
    FOR SELECT USING (salon_id = get_user_salon_id());

-- Los usuarios pueden crear servicios en su salón
CREATE POLICY "Users can create salon services" ON services
    FOR INSERT WITH CHECK (salon_id = get_user_salon_id());

-- Los usuarios pueden actualizar servicios de su salón
CREATE POLICY "Users can update salon services" ON services
    FOR UPDATE USING (salon_id = get_user_salon_id());

-- Solo admins pueden eliminar servicios
CREATE POLICY "Admins can delete salon services" ON services
    FOR DELETE USING (
        is_salon_admin() AND 
        salon_id = get_user_salon_id()
    );

-- Políticas para la tabla PRODUCTS
-- Los usuarios pueden ver productos de su salón
CREATE POLICY "Users can view salon products" ON products
    FOR SELECT USING (salon_id = get_user_salon_id());

-- Los usuarios pueden crear productos en su salón
CREATE POLICY "Users can create salon products" ON products
    FOR INSERT WITH CHECK (salon_id = get_user_salon_id());

-- Los usuarios pueden actualizar productos de su salón
CREATE POLICY "Users can update salon products" ON products
    FOR UPDATE USING (salon_id = get_user_salon_id());

-- Solo admins pueden eliminar productos
CREATE POLICY "Admins can delete salon products" ON products
    FOR DELETE USING (
        is_salon_admin() AND 
        salon_id = get_user_salon_id()
    );

-- Políticas para la tabla APPOINTMENTS
-- Los usuarios pueden ver citas de su salón
CREATE POLICY "Users can view salon appointments" ON appointments
    FOR SELECT USING (salon_id = get_user_salon_id());

-- Los estilistas solo ven sus propias citas (a menos que sean admin)
CREATE POLICY "Stylists can view their own appointments" ON appointments
    FOR SELECT USING (
        stylist_id = auth.uid() OR 
        is_salon_admin()
    );

-- Los usuarios pueden crear citas en su salón
CREATE POLICY "Users can create salon appointments" ON appointments
    FOR INSERT WITH CHECK (salon_id = get_user_salon_id());

-- Los usuarios pueden actualizar citas de su salón
CREATE POLICY "Users can update salon appointments" ON appointments
    FOR UPDATE USING (
        salon_id = get_user_salon_id() AND (
            stylist_id = auth.uid() OR 
            is_salon_admin()
        )
    );

-- Solo admins pueden eliminar citas
CREATE POLICY "Admins can delete salon appointments" ON appointments
    FOR DELETE USING (
        is_salon_admin() AND 
        salon_id = get_user_salon_id()
    );

-- Políticas para la tabla HAIR_DIAGNOSES
-- Los usuarios pueden ver diagnósticos de clientes de su salón
CREATE POLICY "Users can view salon hair diagnoses" ON hair_diagnoses
    FOR SELECT USING (
        client_id IN (
            SELECT id FROM clients WHERE salon_id = get_user_salon_id()
        )
    );

-- Los usuarios pueden crear diagnósticos para clientes de su salón
CREATE POLICY "Users can create salon hair diagnoses" ON hair_diagnoses
    FOR INSERT WITH CHECK (
        client_id IN (
            SELECT id FROM clients WHERE salon_id = get_user_salon_id()
        )
    );

-- Los usuarios pueden actualizar diagnósticos de su salón
CREATE POLICY "Users can update salon hair diagnoses" ON hair_diagnoses
    FOR UPDATE USING (
        client_id IN (
            SELECT id FROM clients WHERE salon_id = get_user_salon_id()
        )
    );

-- Políticas para la tabla DIAGNOSIS_IMAGES
-- Los usuarios pueden ver imágenes de diagnósticos de su salón
CREATE POLICY "Users can view salon diagnosis images" ON diagnosis_images
    FOR SELECT USING (
        diagnosis_id IN (
            SELECT hd.id FROM hair_diagnoses hd
            JOIN clients c ON hd.client_id = c.id
            WHERE c.salon_id = get_user_salon_id()
        )
    );

-- Los usuarios pueden crear imágenes de diagnósticos de su salón
CREATE POLICY "Users can create salon diagnosis images" ON diagnosis_images
    FOR INSERT WITH CHECK (
        diagnosis_id IN (
            SELECT hd.id FROM hair_diagnoses hd
            JOIN clients c ON hd.client_id = c.id
            WHERE c.salon_id = get_user_salon_id()
        )
    );

-- Políticas para la tabla COLOR_FORMULATIONS
-- Los usuarios pueden ver formulaciones de su salón
CREATE POLICY "Users can view salon color formulations" ON color_formulations
    FOR SELECT USING (
        diagnosis_id IN (
            SELECT hd.id FROM hair_diagnoses hd
            JOIN clients c ON hd.client_id = c.id
            WHERE c.salon_id = get_user_salon_id()
        )
    );

-- Los usuarios pueden crear formulaciones para su salón
CREATE POLICY "Users can create salon color formulations" ON color_formulations
    FOR INSERT WITH CHECK (
        diagnosis_id IN (
            SELECT hd.id FROM hair_diagnoses hd
            JOIN clients c ON hd.client_id = c.id
            WHERE c.salon_id = get_user_salon_id()
        )
    );

-- Los usuarios pueden actualizar formulaciones de su salón
CREATE POLICY "Users can update salon color formulations" ON color_formulations
    FOR UPDATE USING (
        diagnosis_id IN (
            SELECT hd.id FROM hair_diagnoses hd
            JOIN clients c ON hd.client_id = c.id
            WHERE c.salon_id = get_user_salon_id()
        )
    );

-- Políticas para la tabla BRANDS (acceso público de lectura)
CREATE POLICY "Anyone can view brands" ON brands
    FOR SELECT USING (true);

-- Solo admins de sistema pueden modificar marcas (esto se manejaría con roles especiales)
CREATE POLICY "System admins can manage brands" ON brands
    FOR ALL USING (false); -- Por ahora, nadie puede modificar

-- Políticas para la tabla PRODUCT_LINES (acceso público de lectura)
CREATE POLICY "Anyone can view product lines" ON product_lines
    FOR SELECT USING (true);

-- Solo admins de sistema pueden modificar líneas de productos
CREATE POLICY "System admins can manage product lines" ON product_lines
    FOR ALL USING (false); -- Por ahora, nadie puede modificar

-- Políticas para la tabla INVENTORY_MOVEMENTS
-- Los usuarios pueden ver movimientos de inventario de su salón
CREATE POLICY "Users can view salon inventory movements" ON inventory_movements
    FOR SELECT USING (
        product_id IN (
            SELECT id FROM products WHERE salon_id = get_user_salon_id()
        )
    );

-- Los usuarios pueden crear movimientos de inventario en su salón
CREATE POLICY "Users can create salon inventory movements" ON inventory_movements
    FOR INSERT WITH CHECK (
        product_id IN (
            SELECT id FROM products WHERE salon_id = get_user_salon_id()
        )
    );

-- Políticas para la tabla AUDIT_LOGS
-- Solo admins pueden ver logs de auditoría
CREATE POLICY "Admins can view audit logs" ON audit_logs
    FOR SELECT USING (is_salon_admin());

-- El sistema puede insertar logs (esto se manejaría con triggers)
CREATE POLICY "System can insert audit logs" ON audit_logs
    FOR INSERT WITH CHECK (true);
