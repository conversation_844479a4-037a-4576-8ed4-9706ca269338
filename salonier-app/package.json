{"name": "salonier-app", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.2.0", "@react-navigation/bottom-tabs": "^7.3.15", "@react-navigation/native": "^7.1.11", "@react-navigation/stack": "^7.3.4", "@supabase/supabase-js": "^2.50.0", "expo": "~53.0.11", "expo-camera": "^16.1.8", "expo-face-detector": "^13.0.2", "expo-image-manipulator": "^13.1.7", "expo-image-picker": "^16.1.4", "expo-secure-store": "^14.2.3", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.3", "react-native-calendars": "^1.1312.1", "react-native-chart-kit": "^6.12.0", "react-native-gesture-handler": "^2.26.0", "react-native-reanimated": "^3.18.0", "react-native-safe-area-context": "^5.4.1", "react-native-screens": "^4.11.1", "react-native-svg": "^15.12.0", "react-native-vector-icons": "^10.2.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}