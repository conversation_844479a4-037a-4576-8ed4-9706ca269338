{"version": 3, "file": "ViewabilityTracker.js", "sourceRoot": "", "sources": ["../../../src/core/ViewabilityTracker.ts"], "names": [], "mappings": ";;;;;;;;;;;AAAA,sDAAiD;AAuBjD;IAkBI,4BAAY,iBAAyB,EAAE,aAAqB;QAA5D,iBAmBC;QAvBO,aAAQ,GAAa,EAAE,CAAC;QAkKxB,mCAA8B,GAAG,UAAC,KAAa;YACnD,IAAM,QAAQ,GAAG,KAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YACtC,KAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,KAAI,CAAC,YAAY,CAAC,CAAC;YACrD,OAAO,KAAI,CAAC,YAAY,CAAC,GAAG,CAAC;QACjC,CAAC,CAAA;QAjKG,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC;QACjD,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;QACpB,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;QACvB,IAAI,CAAC,kBAAkB,GAAG,iBAAiB,CAAC;QAC5C,IAAI,CAAC,cAAc,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;QAC3C,IAAI,CAAC,cAAc,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;QAE3C,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;QAC3B,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;QAEtB,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC,CAAE,oBAAoB;QAChD,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC,CAAE,oBAAoB;QAEhD,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;QACjC,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;QAEjC,IAAI,CAAC,YAAY,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;QACzC,IAAI,CAAC,kBAAkB,GAAG,EAAE,eAAe,EAAE,CAAC,EAAE,aAAa,EAAE,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE,CAAC;IACvF,CAAC;IAEM,iCAAI,GAAX,UAAY,gBAAkC;QAC1C,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,cAAc,EAAE,gBAAgB,CAAC,CAAC;IAC9D,CAAC;IAEM,uCAAU,GAAjB,UAAkB,OAAiB,EAAE,SAAiB;QAClD,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;IAChC,CAAC;IAEM,0CAAa,GAApB,UAAqB,SAAoB,EAAE,YAAqB;QAC5D,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAClC,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC;IAC1E,CAAC;IAEM,yCAAY,GAAnB;QACI,IAAM,iBAAiB,GAAG,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,IAAI,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC;QAClH,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACjD,OAAO,iBAAiB,CAAC;IAC7B,CAAC;IAEM,mDAAsB,GAA7B,UAA8B,MAAc;QACxC,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC;QACzB,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;IAC9D,CAAC;IAEM,yCAAY,GAAnB,UAAoB,MAAc,EAAE,QAAiB,EAAE,gBAAkC;QACrF,IAAI,eAAe,GAAG,MAAM,CAAC;QAC7B,IAAI,QAAQ,EAAE;YACV,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC;YAC5B,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAClD,MAAM,GAAG,CAAC,gBAAgB,CAAC,WAAW,GAAG,gBAAgB,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;SACpF;QAED,IAAI,IAAI,CAAC,cAAc,KAAK,eAAe,EAAE;YACzC,IAAI,CAAC,cAAc,GAAG,eAAe,CAAC;YACtC,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAC;YACtD,IAAI,UAAU,GAAG,CAAC,CAAC;YACnB,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE;gBACjC,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;aACxC;YACD,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;SAClC;IACL,CAAC;IAEM,0CAAa,GAApB;QACI,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAEM,gDAAmB,GAA1B;QACI,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAEM,8CAAiB,GAAxB;QACI,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAEM,2DAA8B,GAArC;QACI,IAAM,aAAa,GAAG,IAAI,CAAC,6BAA6B,CAAC,KAAK,CAAC,CAAC;QAChE,IAAI,MAAM,GAAG,aAAa,CAAC;QAC3B,KAAK,IAAI,CAAC,GAAG,aAAa,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;YACzC,IAAI,IAAI,CAAC,aAAa,EAAE;gBACpB,IAAI,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;oBACvD,MAAM;iBACT;qBAAM;oBACH,MAAM,GAAG,CAAC,CAAC;iBACd;aACJ;iBAAM;gBACH,IAAI,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;oBACvD,MAAM;iBACT;qBAAM;oBACH,MAAM,GAAG,CAAC,CAAC;iBACd;aACJ;SACJ;QACD,OAAO,MAAM,CAAC;IAClB,CAAC;IAEM,oDAAuB,GAA9B,UAA+B,iBAAyB;QACpD,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,iBAAiB,CAAC,CAAC;QACzD,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IACrD,CAAC;IAEM,wDAA2B,GAAlC;QACI,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACnC,CAAC;IACM,4CAAe,GAAtB,UAAuB,YAAoB;QACxC,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;IACrC,CAAC;IAEO,4DAA+B,GAAvC;QACI,IAAI,iBAAiB,GAAG,CAAC,CAAC;QAE1B,0CAA0C;QAC1C,IAAI,IAAI,CAAC,cAAc,GAAG,IAAI,EAAE;YAC5B,iBAAiB,GAAG,IAAI,CAAC,6BAA6B,EAAE,CAAC;SAC5D;aAAM,IAAI,IAAI,CAAC,cAAc,GAAG,CAAC,EAAE;YAChC,iBAAiB,GAAG,IAAI,CAAC,8BAA8B,EAAE,CAAC;SAC7D;QACD,OAAO,iBAAiB,CAAC;IAC7B,CAAC;IAEO,0CAAa,GAArB,UAAsB,UAAkB;QACpC,IAAM,eAAe,GAAa,EAAE,CAAC;QACrC,IAAM,eAAe,GAAa,EAAE,CAAC;QACrC,IAAI,CAAC,WAAW,CAAC,eAAe,EAAE,eAAe,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;QACrE,IAAI,CAAC,WAAW,CAAC,eAAe,EAAE,eAAe,EAAE,UAAU,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC;QAC1E,IAAI,CAAC,wCAAwC,CAAC,eAAe,EAAE,eAAe,CAAC,CAAC;IACpF,CAAC;IAEO,0CAAa,GAArB,UAAsB,MAAc,EAAE,gBAAkC;QACpE,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;QACxD,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAC;QACtD,IAAM,iBAAiB,GAAG,IAAI,CAAC,+BAA+B,EAAE,CAAC;QACjE,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC;IAC1C,CAAC;IAED,iFAAiF;IACzE,2DAA8B,GAAtC;QACI,IAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;QACnC,IAAI,QAAQ,GAAG,IAAI,CAAC;QACpB,IAAM,WAAW,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;QAEzC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;YAC5B,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YAC5B,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;YAC/C,IAAI,IAAI,CAAC,4BAA4B,CAAC,WAAW,CAAC,KAAK,EAAE,WAAW,CAAC,GAAG,CAAC,EAAE;gBACvE,OAAO,CAAC,CAAC;aACZ;SACJ;QACD,OAAO,CAAC,CAAC;IACb,CAAC;IAEO,0DAA6B,GAArC,UAAsC,IAAQ;QAAR,qBAAA,EAAA,QAAQ;QAC1C,IAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;QACnC,OAAO,sBAAY,CAAC,2BAA2B,CAAC,KAAK,EAAE,IAAI,CAAC,cAAc,CAAC,KAAK,GAAG,IAAI,EAAE,IAAI,CAAC,8BAA8B,CAAC,CAAC;IAClI,CAAC;IAQD,oGAAoG;IAC5F,wCAAW,GAAnB,UAAoB,iBAA2B,EAAE,iBAA2B,EAAE,UAAkB,EAAE,SAAkB;QAChH,IAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;QACnC,IAAM,WAAW,GAAU,EAAE,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;QAChD,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,IAAI,iBAAiB,GAAG,KAAK,CAAC;QAC9B,IAAI,UAAU,GAAG,KAAK,EAAE;YACpB,IAAI,CAAC,SAAS,EAAE;gBACZ,KAAK,CAAC,GAAG,UAAU,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;oBACjC,IAAI,IAAI,CAAC,2BAA2B,CAAC,CAAC,EAAE,KAAK,EAAE,WAAW,EAAE,iBAAiB,EAAE,iBAAiB,CAAC,EAAE;wBAC/F,iBAAiB,GAAG,IAAI,CAAC;qBAC5B;yBAAM;wBACH,IAAI,iBAAiB,EAAE;4BACnB,MAAM;yBACT;qBACJ;iBACJ;aACJ;iBAAM;gBACH,KAAK,CAAC,GAAG,UAAU,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;oBAC9B,IAAI,IAAI,CAAC,2BAA2B,CAAC,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,iBAAiB,EAAE,iBAAiB,CAAC,EAAE;wBAC9F,iBAAiB,GAAG,IAAI,CAAC;qBAC5B;yBAAM;wBACH,IAAI,iBAAiB,EAAE;4BACnB,MAAM;yBACT;qBACJ;iBACJ;aACJ;SACJ;IACL,CAAC;IAEO,wDAA2B,GAAnC,UAAoC,KAAa,EACb,WAAoB,EACpB,WAAkB,EAClB,iBAA2B,EAC3B,iBAA2B;QAC3D,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QACtC,IAAI,OAAO,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;QAC/C,IAAI,IAAI,CAAC,4BAA4B,CAAC,WAAW,CAAC,KAAK,EAAE,WAAW,CAAC,GAAG,CAAC,EAAE;YACvE,IAAI,WAAW,EAAE;gBACb,iBAAiB,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;gBACtC,iBAAiB,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;aACzC;iBAAM;gBACH,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC9B,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aACjC;YACD,OAAO,GAAG,IAAI,CAAC;SAClB;aAAM,IAAI,IAAI,CAAC,4BAA4B,CAAC,WAAW,CAAC,KAAK,EAAE,WAAW,CAAC,GAAG,CAAC,EAAE;YAC9E,kCAAkC;YAClC,IAAI,WAAW,EAAE;gBACb,iBAAiB,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;aACzC;iBAAM;gBACH,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aAEjC;YACD,OAAO,GAAG,IAAI,CAAC;SAClB;QACD,OAAO,OAAO,CAAC;IACnB,CAAC;IAEO,+CAAkB,GAA1B,UAA2B,QAAgB,EAAE,WAAkB;QAC3D,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,WAAW,CAAC,GAAG,GAAG,QAAQ,CAAC,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC;YAC9C,WAAW,CAAC,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC;SAClC;aAAM;YACH,WAAW,CAAC,GAAG,GAAG,QAAQ,CAAC,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC;YAC/C,WAAW,CAAC,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC;SAClC;IACL,CAAC;IAEO,4CAAe,GAAvB,UAAwB,MAAa,EAAE,SAAiB;QACpD,OAAO,CAAC,MAAM,CAAC,KAAK,GAAG,SAAS,IAAI,MAAM,CAAC,GAAG,GAAG,SAAS,CAAC,CAAC;IAChE,CAAC;IAEO,sDAAyB,GAAjC,UAAkC,MAAa,EAAE,UAAkB,EAAE,QAAgB;QACjF,OAAO,CAAC,MAAM,CAAC,KAAK,IAAI,UAAU,IAAI,MAAM,CAAC,GAAG,IAAI,QAAQ,CAAC,CAAC;IAClE,CAAC;IAEO,qDAAwB,GAAhC,UAAiC,MAAa,EAAE,UAAkB,EAAE,QAAgB;QAChF,OAAO,UAAU,GAAG,QAAQ,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,KAAK,UAAU,IAAI,MAAM,CAAC,GAAG,KAAK,QAAQ,CAAC,CAAC;IACnG,CAAC;IAEO,kDAAqB,GAA7B,UAA8B,MAAa,EAAE,UAAkB,EAAE,QAAgB;QAC7E,OAAO,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,UAAU,CAAC;YAC3C,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,QAAQ,CAAC;YACtC,IAAI,CAAC,yBAAyB,CAAC,MAAM,EAAE,UAAU,EAAE,QAAQ,CAAC;YAC5D,IAAI,CAAC,wBAAwB,CAAC,MAAM,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;IACpE,CAAC;IAEO,yDAA4B,GAApC,UAAqC,UAAkB,EAAE,QAAgB;QACrE,OAAO,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,cAAc,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;IACjF,CAAC;IAEO,yDAA4B,GAApC,UAAqC,UAAkB,EAAE,QAAgB;QACrE,OAAO,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,cAAc,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;IACjF,CAAC;IAEO,mDAAsB,GAA9B,UAA+B,MAAc,EAAE,UAA4B;QACvE,IAAM,eAAe,GAAG,UAAU,CAAC,WAAW,GAAG,UAAU,CAAC,eAAe,CAAC;QAC5E,IAAM,gBAAgB,GAAG,UAAU,CAAC,WAAW,GAAG,UAAU,CAAC,aAAa,CAAC;QAE3E,IAAM,WAAW,GAAG,MAAM,GAAG,eAAe,CAAC;QAC7C,IAAM,SAAS,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,gBAAgB,CAAC;QAElE,IAAI,CAAC,cAAc,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,WAAW,GAAG,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAC/E,IAAI,CAAC,cAAc,CAAC,GAAG,GAAG,SAAS,GAAG,IAAI,CAAC,kBAAkB,CAAC;QAE9D,IAAI,CAAC,cAAc,CAAC,KAAK,GAAG,WAAW,CAAC;QACxC,IAAI,CAAC,cAAc,CAAC,GAAG,GAAG,SAAS,CAAC;IACxC,CAAC;IAED,0BAA0B;IAClB,qEAAwC,GAAhD,UAAiD,eAAyB,EAAE,eAAyB;QACjG,IAAI,CAAC,sBAAsB,CAAC,eAAe,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;QAC9F,IAAI,CAAC,sBAAsB,CAAC,eAAe,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;QAC9F,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;QACvC,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;IAC3C,CAAC;IAEO,mDAAsB,GAA9B,UAA+B,QAAkB,EAAE,QAAkB,EAAE,IAAiC;QACpG,IAAI,IAAI,EAAE;YACN,IAAM,GAAG,GAAG,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YACzD,IAAM,MAAM,GAAG,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YAC5D,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;gBACrC,IAAI,mBAAK,QAAQ,SAAG,GAAG,EAAE,MAAM,CAAC,CAAC;aACpC;SACJ;IACL,CAAC;IAED,4DAA4D;IACpD,gDAAmB,GAA3B,UAA4B,IAAc,EAAE,IAAc;QACtD,IAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC;QACxB,IAAM,OAAO,GAAG,EAAE,CAAC;QACnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;YAC1B,IAAI,sBAAY,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;gBAChD,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;aACzB;SACJ;QACD,OAAO,OAAO,CAAC;IACnB,CAAC;IACL,yBAAC;AAAD,CAAC,AAnUD,IAmUC"}