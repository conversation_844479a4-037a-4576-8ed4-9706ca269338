{"version": 3, "file": "LayoutProvider.js", "sourceRoot": "", "sources": ["../../../../src/core/dependencies/LayoutProvider.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA,gEAA8F;AAE9F;;;;;;;;;;;;GAYG;AAEH;IAAA;QACI,iGAAiG;QAC1F,+BAA0B,GAAY,IAAI,CAAC;IAuBtD,CAAC;IAbU,gDAAmB,GAA1B,UAA2B,gBAA2B,EAAE,YAAsB,EAAE,aAAwB;QACpG,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,YAAY,EAAE,aAAa,CAAC,CAAC;QAC/F,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACnC,CAAC;IAEM,6CAAgB,GAAvB;QACI,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACnC,CAAC;IAML,yBAAC;AAAD,CAAC,AAzBD,IAyBC;AAzBqB,gDAAkB;AA2BxC;IAAoC,kCAAkB;IAKlD,wBAAY,qBAAyD,EAAE,gBAAgF;QAAvJ,YACI,iBAAO,SAIV;QAHG,KAAI,CAAC,sBAAsB,GAAG,qBAAqB,CAAC;QACpD,KAAI,CAAC,iBAAiB,GAAG,gBAAgB,CAAC;QAC1C,KAAI,CAAC,QAAQ,GAAG,EAAE,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;;IAC5C,CAAC;IAEM,yCAAgB,GAAvB,UAAwB,gBAA2B,EAAE,YAAsB,EAAE,aAAwB;QACjG,OAAO,IAAI,qCAAqB,CAAC,IAAI,EAAE,gBAAgB,EAAE,YAAY,EAAE,aAAa,CAAC,CAAC;IAC1F,CAAC;IAED,yFAAyF;IAClF,8CAAqB,GAA5B,UAA6B,KAAa;QACtC,OAAO,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC;IAC9C,CAAC;IAED,+EAA+E;IAC/E,2FAA2F;IACpF,0CAAiB,GAAxB,UAAyB,IAAqB,EAAE,SAAoB,EAAE,KAAa;QAC/E,OAAO,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;IAC1D,CAAC;IAEM,kDAAyB,GAAhC,UAAiC,SAAoB,EAAE,IAAqB,EAAE,KAAa;QACvF,IAAM,UAAU,GAAG,SAAS,CAAC;QAC7B,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;QACnD,IAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC;QACjC,IAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC9C,IAAI,aAAa,EAAE;YACd,aAAuC,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;SACrE;QACD,OAAO,UAAU,CAAC,MAAM,KAAK,UAAU,CAAC,MAAM,IAAI,UAAU,CAAC,KAAK,KAAK,UAAU,CAAC,KAAK,CAAC;IAC5F,CAAC;IACL,qBAAC;AAAD,CAAC,AArCD,CAAoC,kBAAkB,GAqCrD;AArCY,wCAAc"}