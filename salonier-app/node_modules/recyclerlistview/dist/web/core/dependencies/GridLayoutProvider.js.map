{"version": 3, "file": "GridLayoutProvider.js", "sourceRoot": "", "sources": ["../../../../src/core/dependencies/GridLayoutProvider.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA,mDAA6D;AAE7D,wEAAuE;AAEvE;IAAwC,sCAAc;IAOpD,4BACE,OAAe,EACf,aAAiD,EACjD,OAAkC;IAClC,gGAAgG;IAChG,gBAA2C,EAC3C,uBAAgC;QANlC,YAQE,kBACE,aAAa,EACb,UAAC,IAAqB,EAAE,SAAoB,EAAE,KAAa;YACzD,KAAI,CAAC,SAAS,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;QACnC,CAAC,CACF,SAKF;QAJC,KAAI,CAAC,iBAAiB,GAAG,gBAAgB,CAAC;QAC1C,KAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,KAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,KAAI,CAAC,wBAAwB,GAAG,CAAC,CAAC,uBAAuB,KAAK,SAAS,CAAC,IAAI,CAAC,uBAAuB,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,uBAAuB,CAAC;;IAChJ,CAAC;IAEM,6CAAgB,GAAvB,UAAwB,gBAA2B,EAAE,YAAsB,EAAE,aAAwB;QACnG,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAClC,IAAI,CAAC,iBAAiB,GAAG,gBAAgB,CAAC;QAC1C,OAAO,IAAI,qCAAiB,CAAC,IAAI,EAAE,gBAAgB,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,wBAAwB,EAAE,IAAI,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC;IACvJ,CAAC;IAEO,sCAAS,GAAjB,UAAkB,SAAoB,EAAE,KAAa;QACnD,IAAM,OAAO,GAAW,IAAI,CAAC,QAAQ,CAAC;QACtC,IAAM,QAAQ,GAAW,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QAC9C,IAAI,QAAQ,GAAG,OAAO,EAAE;YACtB,MAAM,IAAI,KAAK,CAAC,sBAAsB,GAAG,KAAK,GAAG,4BAA4B,CAAC,CAAC;SAChF;QACD,IAAI,IAAI,CAAC,iBAAiB,EAAE;YAC1B,IAAI,IAAI,CAAC,aAAa,EAAE;gBACtB,SAAS,CAAC,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;gBAChD,SAAS,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAM,GAAG,OAAO,CAAC,GAAG,QAAQ,CAAC;aAEzE;iBAAM;gBACL,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;gBACjD,SAAS,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,GAAG,OAAO,CAAC,GAAG,QAAQ,CAAC;aACvE;SACF;aAAM;YACL,MAAM,IAAI,KAAK,CAAC,sEAAsE,CAAC,CAAC;SACzF;IACH,CAAC;IACH,yBAAC;AAAD,CAAC,AApDD,CAAwC,+BAAc,GAoDrD;AApDY,gDAAkB"}