{"version": 3, "file": "StickyContainer.js", "sourceRoot": "", "sources": ["../../../src/core/StickyContainer.tsx"], "names": [], "mappings": ";AAAA;;GAEG;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,6BAA+B;AAC/B,sCAAwC;AACxC,6CAA0D;AAI1D,sDAAiD;AACjD,sDAAiD;AACjD,wDAAmD;AACnD,sFAAiF;AAKjF,4DAA2D;AAG3D,qDAAqD;AACrD,IAAM,iBAAiB,GAAY,CAAC;IAChC,8DAA8D;IAC9D,IAAI,KAAK,CAAC,OAAO,EAAE;QACf,IAAM,YAAY,GAAG,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAC/D,OAAO,YAAY,IAAI,EAAE,CAAC;KAC7B;IACD,OAAO,KAAK,CAAC;AACjB,CAAC,CAAC,EAAE,CAAC;AAgBL;IAA6E,mCAAkB;IAc3F,yBAAY,KAAQ,EAAE,OAAa;QAAnC,YACI,kBAAM,KAAK,EAAE,OAAO,CAAC,SAQxB;QArBO,kBAAY,GAA+E,SAAS,CAAC;QAKrG,sBAAgB,GAA2C,IAAI,CAAC;QAChE,sBAAgB,GAA2C,IAAI,CAAC;QAChE,wBAAkB,GAAa,EAAE,CAAC;QAClC,uBAAiB,GAAqB;YAC1C,eAAe,EAAE,CAAC,EAAE,aAAa,EAAE,CAAC,EAAE,WAAW,EAAE,CAAC;SACvD,CAAC;QA+DM,qBAAe,GAAG,UAAC,IAAqB,EAAE,IAAS,EAAE,KAAa,EAAE,aAAsB;YAC9F,IAAI,KAAI,CAAC,KAAK,CAAC,kBAAkB,EAAE;gBAC/B,IAAM,YAAY,GAA0B,KAAI,CAAC,mBAAmB,EAAE,CAAC;gBACvE,IAAM,gBAAgB,GAA0B,KAAI,CAAC,oBAAoB,EAAE,CAAC;gBAC5E,IAAI,YAAY,GAAG,KAAK,CAAC;gBACzB,IAAI,YAAY,IAAI,gBAAgB,EAAE;oBAClC,YAAY,GAAG,gBAAgB,CAAC,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC;iBAChE;gBACD,IAAI,CAAC,YAAY,IAAI,KAAI,CAAC,KAAK,CAAC,mBAAmB;uBAC5C,KAAK,KAAK,KAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC,CAAC,EAAE;oBAChD,OAAO,IAAI,CAAC;iBACf;aACJ;YACD,OAAO,KAAI,CAAC,YAAY,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,aAAa,CAAC,CAAC;QAC/D,CAAC,CAAA;QAEO,qBAAe,GAAG,UAAC,QAAa;YACpC,KAAI,CAAC,YAAY,GAAG,QAAwF,CAAC;YAC7G,IAAM,QAAQ,GAAG,iBAAiB,CAAC,CAAC,CAAC,KAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,KAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC;YAC7F,IAAI,QAAQ,EAAE;gBACV,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE;oBAChC,QAAQ,CAAC,QAAQ,CAAC,CAAC;iBACtB;qBAAM;oBACH,MAAM,IAAI,qBAAW,CAAC,oCAA0B,CAAC,yBAAyB,CAAC,CAAC;iBAC/E;aACJ;QACL,CAAC,CAAA;QAEO,iCAA2B,GAAG;YAClC,OAAO,KAAI,CAAC,iBAAiB,CAAC;QAClC,CAAC,CAAA;QAEO,yBAAmB,GAAG,UAAC,eAAoB;YAC/C,IAAI,KAAI,CAAC,gBAAgB,KAAK,eAAe,EAAE;gBAC3C,KAAI,CAAC,gBAAgB,GAAG,eAA2D,CAAC;gBACpF,+EAA+E;gBAC/E,KAAI,CAAC,yCAAyC,CAAC,KAAI,CAAC,kBAAkB,CAAC,CAAC;aAC3E;QACL,CAAC,CAAA;QAEO,yBAAmB,GAAG,UAAC,eAAoB;YAC/C,IAAI,KAAI,CAAC,gBAAgB,KAAK,eAAe,EAAE;gBAC3C,KAAI,CAAC,gBAAgB,GAAG,eAA2D,CAAC;gBACpF,+EAA+E;gBAC/E,KAAI,CAAC,yCAAyC,CAAC,KAAI,CAAC,kBAAkB,CAAC,CAAC;aAC3E;QACL,CAAC,CAAA;QAEO,8BAAwB,GAAG,UAAC,GAAa,EAAE,GAAa,EAAE,MAAgB;YAC9E,KAAI,CAAC,kBAAkB,GAAG,GAAG,CAAC;YAC9B,KAAI,CAAC,yCAAyC,CAAC,GAAG,CAAC,CAAC;YACpD,IAAI,KAAI,CAAC,KAAK,CAAC,QAAQ,IAAI,KAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,IAAI,KAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,uBAAuB,EAAE;gBACvG,KAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,uBAAuB,CAAC,GAAG,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC;aACvE;QACL,CAAC,CAAA;QAEO,+CAAyC,GAAG,UAAC,GAAa;YAC9D,IAAI,KAAI,CAAC,gBAAgB,EAAE;gBACvB,KAAI,CAAC,gBAAgB,CAAC,uBAAuB,CAAC,GAAG,CAAC,CAAC;aACtD;YACD,IAAI,KAAI,CAAC,gBAAgB,EAAE;gBACvB,KAAI,CAAC,gBAAgB,CAAC,uBAAuB,CAAC,GAAG,CAAC,CAAC;aACtD;QACL,CAAC,CAAA;QAEO,eAAS,GAAG,UAAC,QAAqB,EAAE,OAAe,EAAE,OAAe;YACxE,KAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,OAAO,EAAE,KAAI,CAAC,KAAK,CAAC,CAAC;YACxD,IAAI,KAAI,CAAC,gBAAgB,EAAE;gBACvB,KAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;aAC3C;YACD,IAAI,KAAI,CAAC,gBAAgB,EAAE;gBACvB,KAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;aAC3C;YACD,IAAI,KAAI,CAAC,KAAK,CAAC,QAAQ,IAAI,KAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,EAAE;gBAC3D,KAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;aAClE;QACL,CAAC,CAAA;QAMO,sBAAgB,GAAG;YACvB,IAAI,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,KAAI,CAAC,wBAAwB,EAAE,EAAE;gBACrF,MAAM,IAAI,qBAAW,CAAC,oCAA0B,CAAC,6BAA6B,CAAC,CAAC;aACnF;QACL,CAAC,CAAA;QAEO,8BAAwB,GAAG;YAC/B,OAAO,CACH,KAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,YAAY;mBACnC,KAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,WAAW;mBACrC,KAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,cAAc,CAC9C,CAAC;QACN,CAAC,CAAA;QAEO,wBAAkB,GAAG,UAAC,KAAa;YACvC,IAAI,KAAI,CAAC,YAAY,EAAE;gBACnB,OAAO,KAAI,CAAC,YAAY,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;aAC7C;YACD,OAAO,SAAS,CAAC;QACrB,CAAC,CAAA;QAEO,sBAAgB,GAAG,UAAC,KAAa;YACrC,OAAO,KAAI,CAAC,aAAa,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;QACrD,CAAC,CAAA;QAEO,4BAAsB,GAAG,UAAC,KAAa;YAC3C,OAAO,KAAI,CAAC,eAAe,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;QAC7D,CAAC,CAAA;QAEO,uBAAiB,GAAG;YACxB,OAAO,KAAI,CAAC,cAAc,CAAC;QAC/B,CAAC,CAAA;QAEO,qBAAe,GAAG;YAEtB,OAAO,KAAI,CAAC,YAAY,CAAC;QAC7B,CAAC,CAAA;QAEO,yBAAmB,GAAG;YAC1B,IAAI,KAAI,CAAC,YAAY,EAAE;gBACnB,OAAO,KAAI,CAAC,YAAY,CAAC,eAAe,EAAE,CAAC;aAC9C;YACD,OAAO,SAAS,CAAC;QACrB,CAAC,CAAA;QAEO,0BAAoB,GAAG;YAC3B,IAAI,KAAI,CAAC,YAAY,EAAE;gBACnB,OAAO,KAAI,CAAC,YAAY,CAAC,mBAAmB,EAAE,CAAC;aAClD;YACD,OAAO,SAAS,CAAC;QACrB,CAAC,CAAA;QAEO,4BAAsB,GAAG,UAAC,OAAe,EAAE,OAAe,EAAE,gBAAkC;YAClG,IAAI,KAAI,CAAC,KAAK,CAAC,qBAAqB,EAAE;gBAClC,KAAI,CAAC,KAAK,CAAC,qBAAqB,CAAC,OAAO,EAAE,OAAO,EAAE,gBAAgB,CAAC,CAAC;aACxE;QACL,CAAC,CAAA;QAEO,iBAAW,GAAG,UAAC,KAAQ;YAC3B,IAAM,UAAU,GAA0B,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC;YAC/D,KAAI,CAAC,aAAa,GAAG,UAAU,CAAC,YAAY,CAAC;YAC7C,KAAI,CAAC,eAAe,GAAG,UAAU,CAAC,cAAc,CAAC;YACjD,KAAI,CAAC,cAAc,GAAG,UAAU,CAAC,aAAa,CAAC;YAC/C,KAAI,CAAC,YAAY,GAAG,UAAU,CAAC,WAAW,CAAC;QAC/C,CAAC,CAAA;QA7MG,KAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAM,UAAU,GAA0B,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC;QAC/D,KAAI,CAAC,aAAa,GAAG,UAAU,CAAC,YAAY,CAAC;QAC7C,KAAI,CAAC,eAAe,GAAG,UAAU,CAAC,cAAc,CAAC;QACjD,KAAI,CAAC,cAAc,GAAG,UAAU,CAAC,aAAa,CAAC;QAC/C,KAAI,CAAC,YAAY,GAAG,UAAU,CAAC,WAAW,CAAC;QAC3C,KAAI,CAAC,oBAAoB,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;;IAC3C,CAAC;IAEM,yDAA+B,GAAtC,UAAuC,QAAW;QAC9C,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;IAC/B,CAAC;IAEM,sCAAY,GAAnB;QAAA,iBA4CC;QA3CG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAM,QAAQ,GAAwC,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,wBACrF,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,KAC5B,GAAG,EAAE,IAAI,CAAC,eAAe,EACzB,uBAAuB,EAAE,IAAI,CAAC,wBAAwB,EACtD,QAAQ,EAAE,IAAI,CAAC,SAAS,EACxB,qBAAqB,EAAE,IAAI,CAAC,sBAAsB,EAClD,WAAW,EAAE,IAAI,CAAC,eAAe,IACnC,CAAC;QACH,OAAO,CACH,oBAAC,mBAAI,IAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE;YACzD,QAAQ;YACR,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAC9B,oBAAC,sBAAY,IAAC,GAAG,EAAE,UAAC,eAAoB,IAAK,OAAA,KAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,EAAzC,CAAyC,EAClF,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,mBAAmB,EAC7C,iBAAiB,EAAE,IAAI,CAAC,kBAAkB,EAC1C,eAAe,EAAE,IAAI,CAAC,gBAAgB,EACtC,qBAAqB,EAAE,IAAI,CAAC,sBAAsB,EAClD,gBAAgB,EAAE,IAAI,CAAC,iBAAiB,EACxC,kBAAkB,EAAE,IAAI,CAAC,mBAAmB,EAC5C,mBAAmB,EAAE,IAAI,CAAC,oBAAoB,EAC9C,cAAc,EAAE,IAAI,CAAC,eAAe,EACpC,mBAAmB,EAAE,IAAI,CAAC,KAAK,CAAC,mBAAmB,EACnD,eAAe,EAAE,IAAI,CAAC,KAAK,CAAC,qBAAqB,EACjD,mBAAmB,EAAE,IAAI,CAAC,2BAA2B,GAAI,CAChE,CAAC,CAAC,CAAC,IAAI;YACP,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAC9B,oBAAC,sBAAY,IAAC,GAAG,EAAE,UAAC,eAAoB,IAAK,OAAA,KAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,EAAzC,CAAyC,EAClF,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,mBAAmB,EAC7C,iBAAiB,EAAE,IAAI,CAAC,kBAAkB,EAC1C,eAAe,EAAE,IAAI,CAAC,gBAAgB,EACtC,qBAAqB,EAAE,IAAI,CAAC,sBAAsB,EAClD,gBAAgB,EAAE,IAAI,CAAC,iBAAiB,EACxC,kBAAkB,EAAE,IAAI,CAAC,mBAAmB,EAC5C,mBAAmB,EAAE,IAAI,CAAC,oBAAoB,EAC9C,cAAc,EAAE,IAAI,CAAC,eAAe,EACpC,mBAAmB,EAAE,IAAI,CAAC,KAAK,CAAC,mBAAmB,EACnD,eAAe,EAAE,IAAI,CAAC,KAAK,CAAC,qBAAqB,EACjD,mBAAmB,EAAE,IAAI,CAAC,2BAA2B,EACrD,iBAAiB,EAAI,IAAI,CAAC,KAAK,CAAC,kBAAkB,GAAI,CAC7D,CAAC,CAAC,CAAC,IAAI,CACL,CACV,CAAC;IACN,CAAC;IAgFO,8CAAoB,GAA5B,UAA6B,OAAe,EAAE,OAAe,EAAE,KAA2B;QACtF,OAAO,CAAC,KAAK,CAAC,qBAAqB,IAAI,KAAK,CAAC,qBAAqB,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,IAAI,IAAI,CAAC,iBAAiB,CAAC;IAC5I,CAAC;IA1Ja,yBAAS,GAAG,EAAE,CAAC;IA6NjC,sBAAC;CAAA,AA9ND,CAA6E,iCAAe,GA8N3F;kBA9NoB,eAAe;AAgOpC,eAAe,CAAC,SAAS,GAAG;IAExB,2HAA2H;IAC3H,QAAQ,EAAE,SAAS,CAAC,OAAO,CAAC,UAAU;IAEtC,2IAA2I;IAC3I,uGAAuG;IACvG,sCAAsC;IACtC,mBAAmB,EAAE,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC;IAExD,6FAA6F;IAC7F,sCAAsC;IACtC,mBAAmB,EAAE,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC;IAExD,gIAAgI;IAChI,mBAAmB,EAAE,SAAS,CAAC,IAAI;IAEnC,qGAAqG;IACrG,KAAK,EAAE,SAAS,CAAC,MAAM;IAEvB,kIAAkI;IAClI,qBAAqB,EAAE,SAAS,CAAC,IAAI;IAErC,2JAA2J;IAC3J,kFAAkF;IAClF,+GAA+G;IAC/G,qBAAqB,EAAE,SAAS,CAAC,IAAI;CACxC,CAAC"}