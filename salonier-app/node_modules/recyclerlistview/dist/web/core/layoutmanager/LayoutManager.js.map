{"version": 3, "file": "LayoutManager.js", "sourceRoot": "", "sources": ["../../../../src/core/layoutmanager/LayoutManager.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAKA,yDAAoD;AAEpD;IAAA;IA8CA,CAAC;IA7CU,yCAAiB,GAAxB,UAAyB,KAAa;QAClC,IAAM,OAAO,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QAClC,IAAI,OAAO,CAAC,MAAM,GAAG,KAAK,EAAE;YACxB,OAAO,EAAE,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;SACvD;aAAM;YACH,MAAM,IAAI,qBAAW,CAAC;gBAClB,OAAO,EAAE,iCAAiC,GAAG,KAAK;gBAClD,IAAI,EAAE,4BAA4B;aACrC,CAAC,CAAC;SACN;IACL,CAAC;IAED,wHAAwH;IACjH,iDAAyB,GAAhC,UAAiC,KAAa;QAC1C,OAAO,SAAS,CAAC;IACrB,CAAC;IAED,qCAAqC;IAC9B,oCAAY,GAAnB,UAAoB,KAAa;QAC7B,IAAM,OAAO,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QAClC,IAAI,KAAK,GAAG,OAAO,CAAC,MAAM,EAAE;YACxB,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;SAC5B;QACD,IAAI,KAAK,KAAK,CAAC,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;YACnC,IAAM,WAAW,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YAC/B,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC;YAClB,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC;SACrB;IACL,CAAC;IAiBL,oBAAC;AAAD,CAAC,AA9CD,IA8CC;AA9CqB,sCAAa;AAgDnC;IAA2C,yCAAa;IAQpD,+BAAY,cAA8B,EAAE,gBAA2B,EAAE,YAA6B,EAAE,aAAwB;QAAvD,6BAAA,EAAA,oBAA6B;QAAtG,YACI,iBAAO,SAOV;QANG,KAAI,CAAC,eAAe,GAAG,cAAc,CAAC;QACtC,KAAI,CAAC,OAAO,GAAG,gBAAgB,CAAC;QAChC,KAAI,CAAC,YAAY,GAAG,CAAC,CAAC;QACtB,KAAI,CAAC,WAAW,GAAG,CAAC,CAAC;QACrB,KAAI,CAAC,aAAa,GAAG,CAAC,CAAC,YAAY,CAAC;QACpC,KAAI,CAAC,QAAQ,GAAG,aAAa,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC;;IACvD,CAAC;IAEM,mDAAmB,GAA1B;QACI,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,YAAY,EAAE,KAAK,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC;IAClE,CAAC;IACD;;;;OAIG;IACK,4CAAY,GAAnB,UAAoB,KAAa;QAC9B,iBAAM,YAAY,YAAC,KAAK,CAAC,CAAC;QAC1B,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;YAC5B,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;YACtB,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;SACxB;IACL,CAAC;IAEM,0CAAU,GAAjB;QACI,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAEM,iDAAiB,GAAxB,UAAyB,KAAa;QAClC,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,KAAK,EAAE;YAC9B,OAAO,EAAE,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;SACnE;aAAM;YACH,MAAM,IAAI,qBAAW,CAAC;gBAClB,OAAO,EAAE,iCAAiC,GAAG,KAAK;gBAClD,IAAI,EAAE,4BAA4B;aACrC,CAAC,CAAC;SACN;IACL,CAAC;IAEM,8CAAc,GAArB,UAAsB,KAAa,EAAE,GAAc;QAC/C,IAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QACpC,IAAI,MAAM,EAAE;YACR,MAAM,CAAC,YAAY,GAAG,IAAI,CAAC;YAC3B,MAAM,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC;YACzB,MAAM,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;SAC9B;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,4CAAY,GAAnB,UAAoB,OAAkB;QAClC,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;SAClE;aAAM;YACH,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;SAC/D;IACL,CAAC;IAED,kDAAkD;IAC3C,iDAAiB,GAAxB,UAAyB,UAAkB,EAAE,SAAiB;QAC1D,UAAU,GAAG,IAAI,CAAC,0BAA0B,CAAC,UAAU,CAAC,CAAC;QACzD,IAAI,MAAM,GAAG,CAAC,CAAC;QACf,IAAI,MAAM,GAAG,CAAC,CAAC;QACf,IAAI,QAAQ,GAAG,CAAC,CAAC;QAEjB,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAE3C,IAAI,QAAQ,EAAE;YACV,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC;YACpB,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC;YACpB,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC;SACzC;QAED,IAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;QAC1C,IAAM,OAAO,GAAG,EAAE,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;QACxC,IAAI,QAAQ,GAAG,IAAI,CAAC;QAEpB,IAAI,SAAS,GAAG,IAAI,CAAC;QAErB,KAAK,IAAI,CAAC,GAAG,UAAU,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE;YACzC,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YAC7B,IAAM,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;YACjE,IAAI,SAAS,IAAI,SAAS,CAAC,YAAY,IAAI,SAAS,CAAC,IAAI,KAAK,UAAU,EAAE;gBACtE,OAAO,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC;gBAClC,OAAO,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC;aACnC;iBAAM;gBACH,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC,UAAU,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;aAClE;YACD,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;YAC3B,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE;gBACpE,IAAI,IAAI,CAAC,aAAa,EAAE;oBACpB,MAAM,IAAI,QAAQ,CAAC;oBACnB,MAAM,GAAG,CAAC,CAAC;oBACX,IAAI,CAAC,WAAW,IAAI,QAAQ,CAAC;iBAChC;qBAAM;oBACH,MAAM,GAAG,CAAC,CAAC;oBACX,MAAM,IAAI,QAAQ,CAAC;oBACnB,IAAI,CAAC,YAAY,IAAI,QAAQ,CAAC;iBACjC;gBACD,QAAQ,GAAG,CAAC,CAAC;aAChB;YAED,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;YAEvG,uDAAuD;YACvD,IAAI,CAAC,GAAG,YAAY,GAAG,CAAC,EAAE;gBACtB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,KAAK,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;aAChH;iBAAM;gBACH,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;gBAC5B,QAAQ,CAAC,CAAC,GAAG,MAAM,CAAC;gBACpB,QAAQ,CAAC,CAAC,GAAG,MAAM,CAAC;gBACpB,QAAQ,CAAC,IAAI,GAAG,UAAU,CAAC;gBAC3B,QAAQ,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;gBAC/B,QAAQ,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;aACpC;YAED,IAAI,IAAI,CAAC,aAAa,EAAE;gBACpB,MAAM,IAAI,OAAO,CAAC,MAAM,CAAC;aAC5B;iBAAM;gBACH,MAAM,IAAI,OAAO,CAAC,KAAK,CAAC;aAC3B;SACJ;QACD,IAAI,YAAY,GAAG,SAAS,EAAE;YAC1B,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,EAAE,YAAY,GAAG,SAAS,CAAC,CAAC;SAC7D;QACD,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;IACvC,CAAC;IAEO,sDAAsB,GAA9B,UAA+B,QAAgB;QAC3C,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,CAAC,CAAC;SACjC;aAAM;YACH,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC,CAAC,CAAC;SAClC;IACL,CAAC;IAEO,mDAAmB,GAA3B,UAA4B,QAAgB;QACxC,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;YACxC,IAAI,CAAC,WAAW,IAAI,QAAQ,CAAC;SAChC;aAAM;YACH,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;YACtC,IAAI,CAAC,YAAY,IAAI,QAAQ,CAAC;SACjC;IACL,CAAC;IAEO,0DAA0B,GAAlC,UAAmC,UAAkB;QACjD,IAAI,UAAU,KAAK,CAAC,EAAE;YAClB,OAAO,CAAC,CAAC;SACZ;QACD,IAAI,CAAC,GAAG,UAAU,GAAG,CAAC,CAAC;QACvB,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;YAChB,IAAI,IAAI,CAAC,aAAa,EAAE;gBACpB,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;oBAC1B,MAAM;iBACT;aACJ;iBAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;gBACjC,MAAM;aACT;SACJ;QACD,OAAO,CAAC,CAAC;IACb,CAAC;IAEO,4CAAY,GAApB,UAAqB,KAAa,EAAE,KAAa,EAAE,OAAkB,EAAE,YAAqB;QACxF,OAAO,YAAY,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,OAAO,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC;IACtI,CAAC;IACL,4BAAC;AAAD,CAAC,AA/KD,CAA2C,aAAa,GA+KvD;AA/KY,sDAAqB"}