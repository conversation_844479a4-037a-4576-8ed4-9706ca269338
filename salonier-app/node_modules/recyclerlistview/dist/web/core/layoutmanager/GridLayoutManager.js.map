{"version": 3, "file": "GridLayoutManager.js", "sourceRoot": "", "sources": ["../../../../src/core/layoutmanager/GridLayoutManager.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AACA,iDAAgE;AAGhE;IAAuC,qCAAqB;IAMxD,2BACE,cAA8B,EAC9B,gBAA2B,EAC3B,OAAkC,EAClC,OAAe,EACf,uBAA+B,EAC/B,YAAsB,EACtB,aAAwB;QAP1B,YASE,kBAAM,cAAc,EAAE,gBAAgB,EAAE,YAAY,EAAE,aAAa,CAAC,SAcrE;QAbC,KAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,KAAI,CAAC,iBAAiB,GAAG,YAAY,CAAC;QACtC,KAAI,CAAC,iBAAiB,GAAG,gBAAgB,CAAC;QAC1C,IAAI,uBAAuB,GAAG,CAAC,EAAE;YAC/B,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;SAClE;aAAM;YACL,KAAI,CAAC,wBAAwB,GAAG,uBAAuB,CAAC;SACzD;QACD,IAAI,OAAO,IAAI,CAAC,EAAE;YAChB,MAAM,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAC;SACtE;aAAM;YACL,KAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;SACzB;;IACH,CAAC;IAEM,0CAAc,GAArB,UAAsB,KAAa,EAAE,GAAc;QACjD,uEAAuE;QACvE,yFAAyF;QACzF,8IAA8I;QAC9I,qHAAqH;QACrH,0DAA0D;QAC1D,IAAM,MAAM,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,KAAK,CAAC,CAAC;QACxC,IAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;QACxD,IAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;QACrD,IAAI,MAAM,EAAE;YACV,IAAI,IAAI,CAAC,iBAAiB,EAAE;gBAC1B,IAAI,UAAU,GAAG,IAAI,CAAC,wBAAwB,EAAE;oBAC9C,IAAI,SAAS,KAAK,CAAC,EAAE;wBACnB,OAAO,KAAK,CAAC;qBACd;oBACD,GAAG,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;iBAC5B;aACF;iBAAM;gBACL,IAAI,SAAS,GAAG,IAAI,CAAC,wBAAwB,EAAE;oBAC7C,IAAI,UAAU,KAAK,CAAC,EAAE;wBACpB,OAAO,KAAK,CAAC;qBACd;oBACD,GAAG,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;iBAC1B;aACF;SACF;QACD,OAAO,iBAAM,cAAc,YAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IAC1C,CAAC;IAEM,qDAAyB,GAAhC,UAAiC,KAAa;QAC5C,IAAM,kBAAkB,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QAChD,OAAO,IAAI,CAAC,iBAAiB;YAC3B,CAAC,CAAC;gBACA,MAAM,EACJ,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,kBAAkB;aACvE;YACD,CAAC,CAAC;gBACA,KAAK,EACH,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,kBAAkB;aACtE,CAAC;IACN,CAAC;IACH,wBAAC;AAAD,CAAC,AAxEH,CAAuC,qCAAqB,GAwEzD;AAxEU,8CAAiB"}