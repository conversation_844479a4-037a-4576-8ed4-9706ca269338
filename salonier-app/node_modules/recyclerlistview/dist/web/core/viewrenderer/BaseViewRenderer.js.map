{"version": 3, "file": "BaseViewRenderer.js", "sourceRoot": "", "sources": ["../../../../src/core/viewrenderer/BaseViewRenderer.tsx"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAGA,+DAA8D;AA6B9D;IAA0D,oCAAyC;IAAnG;QAAA,qEAwCC;QAvCU,uBAAiB,GAAY,IAAI,CAAC;;IAuC7C,CAAC;IApCU,gDAAqB,GAA5B,UAA6B,QAAgC;QACzD,IAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,QAAQ,CAAC,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,QAAQ,CAAC,CAAC,CAAC;QAE5E,IAAM,cAAc,GAAG,CAAC,QAAQ,CAAC,8BAA8B;YAC3D,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,KAAK,QAAQ,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,QAAQ,CAAC,MAAM,CAAC;YAC9E,IAAI,CAAC,KAAK,CAAC,cAAc,KAAK,QAAQ,CAAC,cAAc,CAAC;QAE1D,IAAM,uBAAuB,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,KAAK,QAAQ,CAAC,aAAa,CAAC;QACpF,IAAM,0BAA0B,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,KAAK,QAAQ,CAAC,gBAAgB,CAAC;QAC7F,IAAM,cAAc,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,IAAI,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;QAChH,IAAI,YAAY,GAAG,cAAc,IAAI,cAAc,IAAI,uBAAuB,IAAI,0BAA0B,CAAC;QAC7G,IAAI,YAAY,EAAE;YACd,QAAQ,CAAC,YAAY,CAAC,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,EAAY,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC;SACxI;aAAM,IAAI,QAAQ,EAAE;YACjB,YAAY,GAAG,CAAC,QAAQ,CAAC,YAAY,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,EAAY,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC;SACnJ;QACD,OAAO,YAAY,CAAC;IACxB,CAAC;IACM,4CAAiB,GAAxB;QACI,IAAI,CAAC,sBAAsB,GAAG,SAAS,CAAC;QACxC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,EAAY,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IACnH,CAAC;IACM,mDAAwB,GAA/B;QACI,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IACzH,CAAC;IACM,+CAAoB,GAA3B;QACI,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;QAC/B,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,EAAY,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IACtH,CAAC;IACM,6CAAkB,GAAzB;QACI,QAAQ;IACZ,CAAC;IAES,sCAAW,GAArB;QACI,OAAO,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;IACxH,CAAC;IACL,uBAAC;AAAD,CAAC,AAxCD,CAA0D,iCAAe,GAwCxE"}