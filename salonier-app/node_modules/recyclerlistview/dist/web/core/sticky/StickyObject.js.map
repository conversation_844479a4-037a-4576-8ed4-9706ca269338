{"version": 3, "file": "StickyObject.js", "sourceRoot": "", "sources": ["../../../../src/core/sticky/StickyObject.tsx"], "names": [], "mappings": ";AAAA;;GAEG;;;;;;;;;;;;;;;;;;AAEH,6BAA+B;AAC/B,6CAA8D;AAG9D,uFAAkF;AAClF,yDAAoD;AACpD,+DAA8D;AAG9D,IAAY,UAGX;AAHD,WAAY,UAAU;IAClB,+CAAM,CAAA;IACN,+CAAM,CAAA;AACV,CAAC,EAHW,UAAU,GAAV,kBAAU,KAAV,kBAAU,QAGrB;AAeD;IAAgF,gCAAkB;IAqC9F,sBAAY,KAAQ,EAAE,OAAa;QAAnC,YACI,kBAAM,KAAK,EAAE,OAAO,CAAC,SACxB;QArCS,gBAAU,GAAe,UAAU,CAAC,MAAM,CAAC;QAC3C,0BAAoB,GAAW,CAAC,CAAC;QACjC,qBAAe,GAAY,KAAK,CAAC;QAEjC,kBAAY,GAAW,CAAC,CAAC;QACzB,wBAAkB,GAAW,CAAC,CAAC;QAC/B,oBAAc,GAAa,EAAE,CAAC;QAC9B,qBAAe,GAAY,KAAK,CAAC;QAiBnC,uBAAiB,GAAmB,IAAI,uBAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAC1D,0BAAoB,GAAW,CAAC,CAAC;QACjC,sBAAgB,GAAW,CAAC,CAAC;QAC7B,mBAAa,GAAY,IAAI,CAAC;QAC9B,2BAAqB,GAAW,CAAC,CAAC;QAClC,0BAAoB,GAAW,CAAC,CAAC;QACjC,cAAQ,GAAW,CAAC,CAAC;QACrB,uBAAiB,GAAqB;YAC1C,eAAe,EAAE,CAAC,EAAE,aAAa,EAAE,CAAC,EAAE,WAAW,EAAE,CAAC;SACvD,CAAC;;IAIF,CAAC;IAEM,sDAA+B,GAAtC,UAAuC,QAA2B;QAC9D,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAC9B,IAAI,CAAC,2BAA2B,CAAC,QAAQ,CAAC,aAAa,EAAE,IAAI,CAAC,qBAAqB,EAAE,IAAI,CAAC,oBAAoB,EAC1G,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QACtC,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;QAC7C,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;IACxD,CAAC;IAEM,mCAAY,GAAnB;QACI,0DAA0D;QAE1D,IAAM,cAAc,GAAG,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,UAAU,EAAE,IAAI,CAAC,iBAAiB,EAAE,CAAC,EAAE;YAC3E,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,eAAe,IAAI,CAAC,EAAE,QAAQ,EAAE,UAAU,EAAE,KAAK,EAAE,IAAI,CAAC,gBAAgB,EAAE,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;QAEvH,IAAM,OAAO,GAAG,CACZ,oBAAC,uBAAQ,CAAC,IAAI,IAAC,KAAK,EAAE,cAAc,IAC/B,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC,IAAI,CACvC,CACnB,CAAC;QAEF,IAAI,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE;YAC5B,IAAM,cAAc,GAAQ,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE,CAAC;YAC1D,OAAO,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,OAAO,EAAE,IAAI,CAAC,kBAAkB,EAAE,cAAc,CAAC,CAAC;SACvF;aAAM;YACH,OAAO,CAAC,OAAO,CAAC,CAAC;SACpB;IACL,CAAC;IAEM,8CAAuB,GAA9B,UAA+B,GAAa;QACxC,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACxD,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;SAC9B;QACD,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAC9B,IAAI,CAAC,oCAAoC,CAAC,GAAG,CAAC,CAAC;QAC/C,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,IAAI,CAAC,qBAAqB,EAAE,IAAI,CAAC,oBAAoB,EAC5G,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QACtC,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;IACjD,CAAC;IAEM,+BAAQ,GAAf,UAAgB,OAAe;QAC3B,OAAO,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;QAC5C,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAC9B,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QACpD,IAAI,IAAI,CAAC,oBAAoB,KAAK,SAAS,EAAE;YACzC,IAAI,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,oBAAoB,EAAE;gBAC9G,MAAM,IAAI,qBAAW,CAAC,oCAA0B,CAAC,2BAA2B,CAAC,CAAC;aACjF;YACD,IAAM,SAAO,GAAuB,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;YACrF,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,UAAU,IAAI,SAAO,IAAI,SAAO,GAAG,IAAI,CAAC,UAAU,EAAE;gBACjF,IAAI,SAAO,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,eAAe,EAAE;oBAClD,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,oBAAoB,CAAC;oBAC/C,IAAM,SAAS,GAAG,CAAC,SAAO,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC,CAAC;oBACxG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;oBAC3C,IAAI,CAAC,eAAe,EAAE,CAAC;oBACvB,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;iBAChC;aACJ;iBAAM;gBACH,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;aACtC;SACJ;QACD,IAAI,IAAI,CAAC,gBAAgB,KAAK,SAAS,EAAE;YACrC,IAAI,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,oBAAoB,EAAE;gBAC1G,MAAM,IAAI,qBAAW,CAAC,oCAA0B,CAAC,2BAA2B,CAAC,CAAC;aACjF;YACD,IAAM,SAAO,GAAuB,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;YACrF,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,OAAO,IAAI,SAAO,IAAI,SAAO,GAAG,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,OAAO,EAAE;gBAChG,IAAI,SAAO,IAAI,IAAI,CAAC,OAAO,EAAE;oBACzB,IAAM,SAAS,GAAG,CAAC,SAAO,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC,CAAC;oBACpG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;iBAC9C;qBAAM,IAAI,SAAO,GAAG,IAAI,CAAC,OAAO,EAAE;oBAC/B,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,oBAAoB,CAAC;oBAC/C,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;oBACnC,IAAI,CAAC,eAAe,EAAE,CAAC;oBACvB,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;iBAChC;aACJ;iBAAM;gBACH,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;aACtC;SACJ;IACL,CAAC;IAUS,wCAAiB,GAA3B,UAA4B,QAAiB,EAAE,mBAAmC;QAAnC,oCAAA,EAAA,0BAAmC;QAC9E,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC;QAChC,IAAI,mBAAmB,EAAE;YACrB,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;SACrB;IACL,CAAC;IAES,0CAAmB,GAA7B,UAA8B,KAAwB;QAClD,OAAO,CAAC,KAAK,CAAC,mBAAmB,IAAI,KAAK,CAAC,mBAAmB,EAAE,CAAC,IAAI,IAAI,CAAC,iBAAiB,CAAC;IAChG,CAAC;IAES,yCAAkB,GAA5B,UAA6B,OAAe,EAAE,WAAoB;QAC9D,IAAM,kBAAkB,GAAY,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;QAClF,IAAI,IAAI,CAAC,eAAe,KAAK,kBAAkB,EAAE;YAC7C,IAAI,CAAC,eAAe,GAAG,kBAAkB,CAAC;YAC1C,IAAI,IAAI,CAAC,eAAe,EAAE;gBACtB,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;aACjC;iBAAM;gBACH,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;aACrD;SACJ;IACL,CAAC;IAEO,6CAAsB,GAA9B;QACI,IAAM,YAAY,GAA0B,IAAI,CAAC,KAAK,CAAC,kBAAkB,EAAE,CAAC;QAC5E,IAAI,YAAY,EAAE;YACd,IAAI,CAAC,iBAAiB,GAAG,YAAY,CAAC,MAAM,CAAC;YAC7C,IAAI,CAAC,gBAAgB,GAAG,YAAY,CAAC,KAAK,CAAC;SAC9C;QACD,IAAM,gBAAgB,GAA0B,IAAI,CAAC,KAAK,CAAC,mBAAmB,EAAE,CAAC;QACjF,IAAI,gBAAgB,IAAI,IAAI,CAAC,iBAAiB,EAAE;YAC5C,IAAI,CAAC,YAAY,GAAG,gBAAgB,CAAC,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC;SACxE;IACL,CAAC;IAEO,sCAAe,GAAvB,UAAwB,gBAA2B;QAC/C,IAAM,aAAa,GAAyB,gBAAgB,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC;QAC3G,IAAI,aAAa,EAAE;YACf,IAAI,CAAC,kBAAkB,GAAG,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC3D,IAAI,CAAC,oBAAoB,GAAG,aAAa,CAAC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,oBAAoB,CAAC,CAAC;YACzF,IAAI,CAAC,gBAAgB,GAAG,aAAa,CAAC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,oBAAoB,CAAC,CAAC;YACrF,IAAI,IAAI,CAAC,kBAAkB,KAAK,SAAS,EAAE;gBACvC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;gBAC5E,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;gBACzE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC;gBACnF,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;aAChI;YACD,IAAI,IAAI,CAAC,oBAAoB,KAAK,SAAS,EAAE;gBACzC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;gBAC/E,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC;aACzF;YACD,IAAI,IAAI,CAAC,gBAAgB,KAAK,SAAS,EAAE;gBACrC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBACvE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;gBAChE,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC;gBAC1E,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;aAC9G;SACJ;IACL,CAAC;IAEO,2DAAoC,GAA5C,UAA6C,YAAsB;QAC/D,IAAI,CAAC,cAAc,GAAG,YAAY,CAAC;QACnC,IAAI,CAAC,qBAAqB,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;QAC7C,IAAI,CAAC,oBAAoB,GAAG,YAAY,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IACtE,CAAC;IAEO,oCAAa,GAArB;QACI,IAAI,IAAI,CAAC,kBAAkB,KAAK,SAAS,EAAE;YACvC,IAAM,WAAW,GAAQ,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YAC7E,IAAM,iBAAiB,GAAoB,IAAI,CAAC,KAAK,CAAC,qBAAqB,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YACrG,IAAM,cAAc,GAAuB,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE,CAAC;YACzE,IAAM,YAAY,GAC2B,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC;YACzE,IAAI,IAAI,CAAC,KAAK,CAAC,mBAAmB,EAAE;gBAChC,OAAO,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,iBAAiB,EAAE,WAAW,EAAE,IAAI,CAAC,kBAAkB,EAAE,cAAc,CAAC,CAAC;aAClH;iBAAM;gBACH,OAAO,YAAY,CAAC,iBAAiB,EAAE,WAAW,EAAE,IAAI,CAAC,kBAAkB,EAAE,cAAc,CAAC,CAAC;aAChG;SACJ;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,0CAAmB,GAA3B,UAA4B,OAAe;QACvC,OAAO,OAAO,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC;IACtE,CAAC;IACL,mBAAC;AAAD,CAAC,AA3ND,CAAgF,iCAAe,GA2N9F"}