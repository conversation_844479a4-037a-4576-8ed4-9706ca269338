{"version": 3, "file": "ScrollComponent.js", "sourceRoot": "", "sources": ["../../../../../src/platform/web/scrollcomponent/ScrollComponent.tsx"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,6BAA+B;AAE/B,yFAA8G;AAE9G,+CAA0C;AAC1C;;;;GAIG;AAEH;IAA6C,mCAAmB;IAa5D,yBAAY,IAA0B;QAAtC,YACI,kBAAM,IAAI,CAAC,SAGd;QANO,oBAAc,GAA0B,IAAI,CAAC;QAwC7C,eAAS,GAAG,UAAC,CAAc;YAC/B,KAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACzF,CAAC,CAAA;QAEO,oBAAc,GAAG,UAAC,KAAgB;YACtC,IAAI,KAAI,CAAC,KAAK,CAAC,aAAa,EAAE;gBAC1B,KAAI,CAAC,KAAK,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;aACnC;QACL,CAAC,CAAA;QA5CG,KAAI,CAAC,OAAO,GAAG,CAAC,CAAC;QACjB,KAAI,CAAC,MAAM,GAAG,CAAC,CAAC;;IACpB,CAAC;IAEM,kCAAQ,GAAf,UAAgB,CAAS,EAAE,CAAS,EAAE,QAAiB;QACnD,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAA,EAAE,CAAC,GAAA,EAAE,QAAQ,UAAA,EAAE,CAAC,CAAC;SACpD;IACL,CAAC;IAEM,gCAAM,GAAb;QAAA,iBAwBC;QAvBG,IAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,kBAAyB,CAAC,CAAC,KAAK;QAC5D,OAAO,CACH,oBAAC,QAAQ,aAAC,GAAG,EAAE,UAAC,UAA0B,IAAK,OAAA,KAAI,CAAC,cAAc,GAAG,UAAqC,EAA3D,CAA2D,IAClG,IAAI,CAAC,KAAK,IACd,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,YAAY,EACnC,QAAQ,EAAE,IAAI,CAAC,SAAS,EACxB,aAAa,EAAE,IAAI,CAAC,cAAc;YAElC,6BAAK,KAAK,EAAE;oBACR,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,aAAa;oBAChC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,YAAY;iBACjC,IACI,IAAI,CAAC,KAAK,CAAC,QAAQ,CAClB;YACL,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,6BAAK,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC;oBAC7D,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,YAAY;oBAC7B,QAAQ,EAAE,UAAU;oBACpB,GAAG,EAAE,CAAC;iBACT,CAAC,CAAC,CAAC,SAAS,IACR,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,CACxB,CAAC,CAAC,CAAC,IAAI,CACN,CACd,CAAC;IACN,CAAC;IAhDa,4BAAY,GAAG;QACzB,aAAa,EAAE,CAAC;QAChB,YAAY,EAAE,CAAC;QACf,kBAAkB,EAAE,sBAAY;QAChC,YAAY,EAAE,KAAK;QACnB,cAAc,EAAE,EAAE;QAClB,aAAa,EAAE,KAAK;KACvB,CAAC;IAoDN,sBAAC;CAAA,AA5DD,CAA6C,6BAAmB,GA4D/D;kBA5DoB,eAAe"}