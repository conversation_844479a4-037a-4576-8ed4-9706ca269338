{"version": 3, "file": "ViewRenderer.js", "sourceRoot": "", "sources": ["../../../../../src/platform/web/viewrenderer/ViewRenderer.tsx"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,6BAA+B;AAG/B,gFAAkG;AAElG;;;;;GAKG;AACH;IAA0C,gCAAqB;IAA/D;QAAA,qEA+FC;QA9FW,UAAI,GAAc,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;QAC1C,cAAQ,GAA0B,IAAI,CAAC;QAEvC,0BAAoB,GAAY,KAAK,CAAC;QA6DtC,aAAO,GAAG,UAAC,GAA0B;YACzC,KAAI,CAAC,QAAQ,GAAG,GAAG,CAAC;QACxB,CAAC,CAAA;;IA4BL,CAAC;IA1FU,wCAAiB,GAAxB;QAAA,iBAWC;QAVG,iBAAM,iBAAiB,WAAE,CAAC;QAC1B,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,cAAc,EAAE;YACvC,IAAI,CAAC,aAAa,GAAG,IAAI,cAAc,CAAC;gBACpC,KAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;YAChC,CAAC,CAAC,CAAC;YACH,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACf,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;aAC7C;SACJ;IACL,CAAC;IAEM,yCAAkB,GAAzB;QACI,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC;QAClC,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC5B,CAAC;IAEM,2CAAoB,GAA3B;QACI,iBAAM,oBAAoB,WAAE,CAAC;QAC7B,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,CAAC;YAChC,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC;SAClC;IACL,CAAC;IAEM,mCAAY,GAAnB;QACI,IAAM,KAAK,GAAkB,IAAI,CAAC,KAAK,CAAC,8BAA8B;YAClE,CAAC,8BACG,SAAS,EAAE,IAAI,CAAC,aAAa,EAAE,EAC/B,eAAe,EAAE,IAAI,CAAC,aAAa,EAAE,IAClC,MAAM,CAAC,aAAa,GACpB,IAAI,CAAC,KAAK,CAAC,cAAc,GACzB,IAAI,CAAC,sBAAsB,EAElC,CAAC,8BACG,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,EACzB,QAAQ,EAAE,QAAQ,EAClB,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,EACvB,SAAS,EAAE,IAAI,CAAC,aAAa,EAAE,EAC/B,eAAe,EAAE,IAAI,CAAC,aAAa,EAAE,IAClC,MAAM,CAAC,aAAa,GACpB,IAAI,CAAC,KAAK,CAAC,cAAc,GACzB,IAAI,CAAC,sBAAsB,CACjC,CAAC;QACN,IAAM,KAAK,GAAG;YACV,KAAK,OAAA;YACL,GAAG,EAAE,IAAI,CAAC,OAAO;SACpB,CAAC;QACF,OAAO,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,WAAW,EAAE,CAAgB,CAAC;IAC3F,CAAC;IAES,6BAAM,GAAhB;QACI,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAEO,2CAAoB,GAA5B,UAA6B,KAAa,EAAE,WAAmC,EAAE,QAAyB;QACtG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,mBAAmB,IAAI,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,KAAK,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,wCAAS,KAAK,GAAG,QAAQ,CAAO,CAAC,CAAC;IAClJ,CAAC;IAMO,oCAAa,GAArB;QACI,OAAO,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC;IACtE,CAAC;IAEO,uCAAgB,GAAxB,UAAyB,YAA6B;QAA7B,6BAAA,EAAA,oBAA6B;QAClD,IAAI,IAAI,CAAC,KAAK,CAAC,8BAA8B,IAAI,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE;YACvE,IAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;YAC9B,IAAI,OAAO,EAAE;gBACT,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,WAAW,CAAC;gBACtC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,YAAY,CAAC;gBACxC,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;oBAChF,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;oBACjC,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;iBACzD;qBAAM,IAAI,YAAY,IAAI,IAAI,CAAC,oBAAoB,EAAE;oBAClD,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;iBACzD;aACJ;SACJ;QACD,IAAI,CAAC,eAAe,EAAE,CAAC;IAC3B,CAAC;IAEO,sCAAe,GAAvB;QACI,IAAI,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE;YACzB,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;SAC7C;IACL,CAAC;IACL,mBAAC;AAAD,CAAC,AA/FD,CAA0C,0BAAgB,GA+FzD;;AAED,IAAM,MAAM,GAAqC;IAC7C,aAAa,EAAE;QACX,UAAU,EAAE,SAAS;QACrB,WAAW,EAAE,CAAC;QACd,WAAW,EAAE,OAAO;QACpB,SAAS,EAAE,YAAY;QACvB,OAAO,EAAE,MAAM;QACf,aAAa,EAAE,QAAQ;QACvB,MAAM,EAAE,CAAC;QACT,OAAO,EAAE,CAAC;QACV,QAAQ,EAAE,UAAU;QACpB,SAAS,EAAE,CAAC;QACZ,QAAQ,EAAE,CAAC;QACX,IAAI,EAAE,CAAC;QACP,GAAG,EAAE,CAAC;KACT;CACJ,CAAC"}