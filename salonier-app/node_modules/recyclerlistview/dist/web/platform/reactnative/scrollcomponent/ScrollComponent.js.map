{"version": 3, "file": "ScrollComponent.js", "sourceRoot": "", "sources": ["../../../../../src/platform/reactnative/scrollcomponent/ScrollComponent.tsx"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,6BAA+B;AAC/B,6CAMsB;AACtB,yFAA8G;AAC9G,gDAA2C;AAC3C;;;;GAIG;AAEH;IAA6C,mCAAmB;IAe5D,yBAAY,IAA0B;QAAtC,YACI,kBAAM,IAAI,CAAC,SAKd;QARO,oBAAc,GAAsB,IAAI,CAAC;QA4EzC,uBAAiB,GAAG,UAAC,UAAe,IAAO,KAAI,CAAC,cAAc,GAAG,UAAiC,CAAC,CAAC,CAAC,CAAC;QAEtG,eAAS,GAAG,UAAC,KAA+C;YAChE,IAAI,KAAK,EAAE;gBACP,IAAM,aAAa,GAAG,KAAK,CAAC,WAAW,CAAC,aAAa,CAAC;gBACtD,KAAI,CAAC,OAAO,GAAG,KAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC;gBAC3E,KAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;aAChE;QACL,CAAC,CAAA;QAEO,eAAS,GAAG,UAAC,KAAwB;YACzC,IAAI,KAAI,CAAC,OAAO,KAAK,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,IAAI,KAAI,CAAC,MAAM,KAAK,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,EAAE;gBACpG,KAAI,CAAC,OAAO,GAAG,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC;gBAC/C,KAAI,CAAC,MAAM,GAAG,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC;gBAC7C,IAAI,KAAI,CAAC,KAAK,CAAC,aAAa,EAAE;oBAC1B,KAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC;oBACrC,KAAI,CAAC,KAAK,CAAC,aAAa,CAAC,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;iBACtD;aACJ;YACD,IAAI,KAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;gBACrB,KAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;aAC9B;QACL,CAAC,CAAA;QA9FG,KAAI,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAChE,KAAI,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC9D,KAAI,CAAC,OAAO,GAAG,CAAC,CAAC;QACjB,KAAI,CAAC,wBAAwB,GAAG,KAAK,CAAC;;IAC1C,CAAC;IAEM,kCAAQ,GAAf,UAAgB,CAAS,EAAE,CAAS,EAAE,UAAmB;QACrD,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAA,EAAE,CAAC,GAAA,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC,CAAC;SAChE;IACL,CAAC;IAEM,2CAAiB,GAAxB;QACI,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,iBAAiB,EAAE;YAChE,OAAO,IAAI,CAAC,cAAc,CAAC,iBAAiB,EAAE,CAAC;SAChD;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,4CAAkB,GAAzB;QACI,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAEM,gCAAM,GAAb;QACI,IAAM,QAAQ,GAAG,gBAAM,CAAC,IAAI,CAAa,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC,CAAC,KAAK;QAC9E,IAAM,sBAAsB,GAAG,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC;QAC9H,IAAM,qBAAqB,GAAG;YAC1B,KAAK,EAAE;gBACH,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,aAAa;gBAChC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,YAAY;aACjC;YACD,UAAU,EAAG,IAAI,CAAC,KAAK,CAAC,YAAY;YACpC,YAAY,EAAG,IAAI,CAAC,OAAO;YAC3B,iBAAiB,EAAE,IAAI,CAAC,KAAK,CAAC,iBAAiB;YAC/C,UAAU,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAiB;SACpG,CAAC;QACF,YAAY;QACZ,UAAU;QACV,uBAAuB;QACvB,qBAAqB;QACrB,oBAAoB;QACpB,0BAA0B;QAC1B,qBAAqB;QACrB,oBAAoB;QACpB,oBAAoB;QACpB,sBAAsB;QACtB,gBAAgB;QAChB,kBAAkB;QAClB,OAAO,CACH,oBAAC,QAAQ,aAAC,GAAG,EAAE,IAAI,CAAC,iBAAiB,EACjC,qBAAqB,EAAE,KAAK,EAC5B,mBAAmB,EAAE,IAAI,CAAC,KAAK,CAAC,cAAc,IAC1C,IAAI,CAAC,KAAK,IACd,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,YAAY,EACnC,QAAQ,EAAE,IAAI,CAAC,SAAS,EACxB,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,wBAAwB,IAAI,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ;YAC7G,oBAAC,mBAAI,IAAC,KAAK,EAAE,EAAE,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,EAAE;gBACrE,sBAAsB,CAAC,qBAAqB,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;gBAClE,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC,IAAI,CACxD,CACA,CACd,CAAC;IACN,CAAC;IAEO,2CAAiB,GAAzB,UAA0B,KAAa,EAAE,QAAyB;QAC9D,OAAO,CACH,oBAAC,mBAAI,eAAK,KAAK,GACV,QAAQ,CACN,CACV,CAAC;IACN,CAAC;IAtFa,4BAAY,GAAG;QACzB,aAAa,EAAE,CAAC;QAChB,YAAY,EAAE,CAAC;QACf,kBAAkB,EAAE,gBAAM,CAAC,IAAI,CAAC,yBAAU,CAAC;QAC3C,YAAY,EAAE,KAAK;QACnB,cAAc,EAAE,EAAE;KACrB,CAAC;IAyGN,sBAAC;CAAA,AAhHD,CAA6C,6BAAmB,GAgH/D;kBAhHoB,eAAe"}