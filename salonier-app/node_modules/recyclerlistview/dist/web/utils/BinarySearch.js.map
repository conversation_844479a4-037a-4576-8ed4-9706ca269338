{"version": 3, "file": "BinarySearch.js", "sourceRoot": "", "sources": ["../../../src/utils/BinarySearch.ts"], "names": [], "mappings": ";;AAAA,8DAAyD;AAMzD;IAAA;IA2IA,CAAC;IA1IiB,wCAA2B,GAAzC,UAA0C,IAAY,EAAE,WAAmB,EAAE,cAAyC;QAClH,IAAI,GAAG,GAAG,CAAC,CAAC;QACZ,IAAI,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC;QACpB,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;QACvC,IAAI,SAAS,GAAG,CAAC,CAAC;QAClB,IAAI,gBAAgB,GAAG,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC,CAAC;QACnE,IAAI,MAAM,GAAG,GAAG,CAAC;QACjB,IAAI,IAAI,GAAG,CAAC,CAAC;QACb,IAAI,YAAY,GAAG,CAAC,CAAC;QAErB,IAAI,gBAAgB,KAAK,CAAC,EAAE;YACxB,OAAO,MAAM,CAAC;SACjB;QAED,IAAI,IAAI,GAAG,CAAC,EAAE;YACV,MAAM,IAAI,qBAAW,CAAC;gBAClB,OAAO,EAAE,gCAAgC;gBACzC,IAAI,EAAE,uBAAuB;aAChC,CAAC,CAAC;SACN;QAED,OAAO,GAAG,IAAI,IAAI,EAAE;YAChB,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;YACnC,SAAS,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC;YAChC,IAAI,GAAG,SAAS,GAAG,WAAW,CAAC;YAC/B,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAC9B,IAAI,IAAI,IAAI,CAAC,IAAI,YAAY,GAAG,gBAAgB,EAAE;gBAC9C,gBAAgB,GAAG,YAAY,CAAC;gBAChC,MAAM,GAAG,GAAG,CAAC;aAChB;YACD,IAAI,WAAW,GAAG,SAAS,EAAE;gBACzB,IAAI,GAAG,GAAG,GAAG,CAAC,CAAC;aAClB;iBAAM,IAAI,WAAW,GAAG,SAAS,EAAE;gBAChC,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;aACjB;iBAAM;gBACH,OAAO,GAAG,CAAC;aACd;SACJ;QACD,OAAO,MAAM,CAAC;IAClB,CAAC;IACa,qCAAwB,GAAtC,UAAuC,MAAgB,EAAE,MAAc;QACnE,IAAI,GAAG,GAAG,CAAC,CAAC;QACZ,IAAI,IAAI,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;QAC7B,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;QACvC,IAAI,QAAQ,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;QAC3B,IAAI,YAAY,GAAG,QAAQ,GAAG,CAAC,CAAC;QAEhC,OAAO,GAAG,IAAI,IAAI,IAAI,QAAQ,KAAK,YAAY,EAAE;YAC7C,IAAI,QAAQ,KAAK,MAAM,EAAE;gBACrB,MAAM;aACT;iBAAM,IAAI,QAAQ,GAAG,MAAM,EAAE;gBAC1B,GAAG,GAAG,GAAG,CAAC;aACb;iBAAM,IAAI,QAAQ,GAAG,MAAM,EAAE;gBAC1B,IAAI,GAAG,GAAG,CAAC;aACd;YACD,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;YACnC,YAAY,GAAG,QAAQ,CAAC;YACxB,QAAQ,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;SAC1B;QACD,OAAO;YACH,KAAK,EAAE,QAAQ;YACf,KAAK,EAAE,GAAG;SACb,CAAC;IACN,CAAC;IACD;;OAEG;IACW,uCAA0B,GAAxC,UAAyC,MAAgB,EAAE,MAAc;QACrE,IAAM,GAAG,GAAG,CAAC,CAAC;QACd,IAAM,IAAI,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;QAC/B,IAAI,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE;YACxB,OAAO;gBACH,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC;gBACnB,KAAK,EAAE,IAAI;aACd,CAAC;SACL;aAAM,IAAI,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,EAAE;YAC7B,OAAO,SAAS,CAAC;SACpB;QACD,IAAM,gBAAgB,GAAkB,IAAI,CAAC,wBAAwB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QACtF,IAAM,QAAQ,GAAW,gBAAgB,CAAC,KAAK,CAAC;QAChD,IAAM,GAAG,GAAW,gBAAgB,CAAC,KAAK,CAAC;QAC3C,IAAI,QAAQ,IAAI,MAAM,EAAE;YACpB,OAAO;gBACH,KAAK,EAAE,QAAQ;gBACf,KAAK,EAAE,GAAG;aACb,CAAC;SACL;aAAM;YACH,OAAO;gBACH,KAAK,EAAE,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC;gBACtB,KAAK,EAAE,GAAG,GAAG,CAAC;aACjB,CAAC;SACL;IACL,CAAC;IACD;;OAEG;IACW,sCAAyB,GAAvC,UAAwC,MAAgB,EAAE,MAAc;QACpE,IAAM,GAAG,GAAG,CAAC,CAAC;QACd,IAAM,IAAI,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;QAC/B,IAAI,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,EAAE;YACtB,OAAO;gBACH,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC;gBAClB,KAAK,EAAE,GAAG;aACb,CAAC;SACL;aAAM,IAAI,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,EAAE;YAC9B,OAAO,SAAS,CAAC;SACpB;QACD,IAAM,gBAAgB,GAAkB,IAAI,CAAC,wBAAwB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QACtF,IAAM,QAAQ,GAAW,gBAAgB,CAAC,KAAK,CAAC;QAChD,IAAM,GAAG,GAAW,gBAAgB,CAAC,KAAK,CAAC;QAC3C,IAAI,QAAQ,IAAI,MAAM,EAAE;YACpB,OAAO;gBACH,KAAK,EAAE,QAAQ;gBACf,KAAK,EAAE,GAAG;aACb,CAAC;SACL;aAAM;YACH,OAAO;gBACH,KAAK,EAAE,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC;gBACtB,KAAK,EAAE,GAAG,GAAG,CAAC;aACjB,CAAC;SACL;IACL,CAAC;IACa,wBAAW,GAAzB,UAA0B,KAAe,EAAE,KAAa;QACpD,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;QAC1B,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,OAAO,CAAC,GAAG,MAAM,EAAE;YACf,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YACxB,IAAI,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,EAAE;gBAClB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;aACb;iBAAM,IAAI,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,EAAE;gBACzB,MAAM,GAAG,CAAC,CAAC;aACd;iBAAM;gBACH,OAAO,CAAC,CAAC;aACZ;SACJ;QACD,OAAO,CAAC,CAAC,CAAC;IACd,CAAC;IACL,mBAAC;AAAD,CAAC,AA3ID,IA2IC"}