{"version": 3, "file": "VirtualRenderer.js", "sourceRoot": "", "sources": ["../../../src/core/VirtualRenderer.ts"], "names": [], "mappings": ";;AAAA,4DAAuD;AAEvD,wDAAmD;AACnD,sFAAiF;AAEjF,2DAAkG;AAClG,mDAAsD;AACtD,0CAAqC;AAyBrC;IAuBI,yBAAY,kBAAsD,EACtD,kBAA0C,EAC1C,aAA+B,EAC/B,kBAA2B;QAHvC,iBAyBC;QAlCO,oBAAe,GAAuB,gBAAM,CAAC,IAAI,CAAqB,IAAI,CAAC,CAAC,CAAC,KAAK;QAClF,iBAAY,GAAoB,gBAAM,CAAC,IAAI,CAAkB,IAAI,CAAC,CAAC,CAAC,KAAK;QAGzE,mBAAc,GAAyB,IAAI,CAAC;QAC5C,wBAAmB,GAA8B,IAAI,CAAC;QAEtD,2BAAsB,GAAY,KAAK,CAAC;QA+TxC,2BAAsB,GAAG,UAAC,GAAa,EAAE,GAAa,EAAE,MAAgB;YAC5E,IAAI,KAAI,CAAC,qBAAqB,EAAE;gBAC5B,KAAI,CAAC,qBAAqB,CAAC,GAAG,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC;aAChD;QACL,CAAC,CAAA;QAEO,2BAAsB,GAAG,UAAC,GAAa,EAAE,GAAa,EAAE,MAAgB;YAC5E,IAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC;YAC5B,IAAI,WAAW,CAAC;YAChB,IAAI,eAAe,GAAG,CAAC,CAAC;YACxB,IAAI,KAAI,CAAC,mBAAmB,EAAE;gBAC1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;oBAC5B,eAAe,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;oBAC5B,OAAO,KAAI,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;oBAC7C,IAAI,KAAI,CAAC,OAAO,IAAI,eAAe,GAAG,KAAI,CAAC,OAAO,CAAC,SAAS,EAAE;wBAC1D,gHAAgH;wBAChH,8CAA8C;wBAC9C,WAAW,GAAG,KAAI,CAAC,uBAAuB,CAAC,KAAI,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC,CAAC;wBACjF,IAAI,CAAC,4BAAU,CAAC,iBAAiB,CAAC,WAAW,CAAC,EAAE;4BAC5C,KAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,KAAI,CAAC,eAAe,CAAC,qBAAqB,CAAC,eAAe,CAAC,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC;yBACrH;qBACJ;iBACJ;aACJ;YACD,IAAI,KAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,EAAE;gBAC9B,oCAAoC;gBACpC,KAAI,CAAC,mBAAmB,CAAC,KAAI,CAAC,YAAY,CAAC,CAAC;aAC/C;QACL,CAAC,CAAA;QArVG,wEAAwE;QACxE,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;QAEvB,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;QAEpC,mIAAmI;QACnI,IAAI,CAAC,uBAAuB,GAAG,EAAE,CAAC;QAClC,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;QAC1B,IAAI,CAAC,mBAAmB,GAAG,kBAAkB,CAAC;QAC9C,IAAI,CAAC,mBAAmB,GAAG,kBAAkB,CAAC;QAC9C,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QACxB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACpB,IAAI,CAAC,mBAAmB,GAAG,kBAAkB,CAAC;QAE9C,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC;QACnC,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QAExB,4CAA4C;QAC5C,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;QAEnB,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;IACtC,CAAC;IAEM,4CAAkB,GAAzB;QACI,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,OAAO,IAAI,CAAC,cAAc,CAAC,mBAAmB,EAAE,CAAC;SACpD;QACD,OAAO,EAAE,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;IACnC,CAAC;IAEM,kDAAwB,GAA/B,UAAgC,cAAuB;QACnD,IAAI,CAAC,sBAAsB,GAAG,cAAc,CAAC;IACjD,CAAC;IAEM,yDAA+B,GAAtC;QACI,OAAO,IAAI,CAAC,sBAAsB,CAAC;IACvC,CAAC;IAEM,sCAAY,GAAnB,UAAoB,OAAe,EAAE,OAAe,EAAE,QAAiB,EAAE,UAA4B;QACjG,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1B,IAAM,MAAM,GAAG,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC;YAC7E,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE;gBAC7B,IAAI,QAAQ,EAAE;oBACV,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;iBACpD;gBACD,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,CAAC;aAC5C;YACD,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,MAAM,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC;SACvE;IACL,CAAC;IAEM,oDAA0B,GAAjC,UAAkC,QAA8B;QAC5D,IAAI,CAAC,qBAAqB,GAAG,QAAQ,CAAC;IAC1C,CAAC;IAEM,oDAA0B,GAAjC;QACI,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;QAElC,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1B,IAAI,CAAC,mBAAmB,CAAC,oBAAoB,GAAG,IAAI,CAAC;SACxD;IACL,CAAC;IAEM,0CAAgB,GAAvB;QACI,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAEM,gDAAsB,GAA7B,UAA8B,MAAyB,EAAE,GAAc;QACnE,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC;IAC3B,CAAC;IAEM,0CAAgB,GAAvB,UAAwB,aAA4B;QAChD,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;QACpC,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;SACpE;IACL,CAAC;IAEM,2CAAiB,GAAxB,UAAyB,cAAkC;QACvD,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;IAC1C,CAAC;IAEM,+CAAqB,GAA5B;QACI,OAAO,IAAI,CAAC,mBAAmB,CAAC;IACpC,CAAC;IAEM,2CAAiB,GAAxB;QACI,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1B,IAAI,iBAAiB,GAAG,IAAI,CAAC,mBAAmB,CAAC,8BAA8B,EAAE,CAAC;YAClF,IAAI,CAAC,0BAA0B,EAAE,CAAC;YAClC,IAAI,MAAM,GAAG,CAAC,CAAC;YACf,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,OAAO,EAAE;gBACrC,iBAAiB,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,CAAC,EAAE,iBAAiB,CAAC,CAAC;gBAC5E,IAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;gBACvE,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;gBAChC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;aAC1D;YACD,IAAI,CAAC,mBAAmB,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC;SAC3D;IACL,CAAC;IAEM,iCAAO,GAAd;QACI,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1B,IAAI,CAAC,0BAA0B,EAAE,CAAC;YAClC,IAAI,CAAC,mBAAmB,CAAC,YAAY,EAAE,CAAC;SAC3C;IACL,CAAC;IAEM,0CAAgB,GAAvB;QACI,IAAI,MAAM,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;QAC5B,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,IAAM,kBAAkB,GAAG,yBAAO,CAAC,KAAK,CAAS,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;YACrF,IAAI,kBAAkB,GAAG,CAAC,IAAI,IAAI,CAAC,cAAc,EAAE;gBAC/C,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,CAAC;gBACnE,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;aAChF;iBAAM;gBACH,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE;oBAC3B,MAAM,CAAC,CAAC,GAAG,yBAAO,CAAC,KAAK,CAAS,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;oBAChE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;iBAChB;qBAAM;oBACH,MAAM,CAAC,CAAC,GAAG,yBAAO,CAAC,KAAK,CAAS,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;oBAChE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;iBAChB;aACJ;SACJ;QACD,OAAO,MAAM,CAAC;IAClB,CAAC;IAEM,8BAAI,GAAX;QACI,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,YAAY,GAAG,IAAI,yBAAe,EAAE,CAAC;QAC1C,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,IAAI,CAAC,mBAAmB,GAAG,IAAI,4BAAkB,CAC7C,yBAAO,CAAC,KAAK,CAAS,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,CAAC,CAAC,EACxD,yBAAO,CAAC,KAAK,CAAS,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC,CAAC;SAC7D;aAAM;YACH,IAAI,CAAC,mBAAmB,GAAG,IAAI,4BAAkB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;SAC3D;QACD,IAAI,CAAC,0BAA0B,EAAE,CAAC;IACtC,CAAC;IAEM,iDAAuB,GAA9B,UAA+B,gBAAkC;QAC7D,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1B,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;YAClC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;SACnD;IACL,CAAC;IAEM,uCAAa,GAApB,UAAqB,KAAa,EAAE,wBAA2C,EAC1D,cAA4B,EAC5B,gBAA4C;QAC7D,IAAM,WAAW,GAAG,wBAAwB,CAAC,CAAC,CAAC,wBAAwB,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC;QAC9F,IAAM,WAAW,GAAG,cAAc,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC;QACxE,IAAM,YAAY,GAAG,IAAI,CAAC,uBAAuB,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC;QACtE,IAAI,GAAG,GAAG,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC;QAEtD,IAAI,4BAAU,CAAC,iBAAiB,CAAC,GAAG,CAAC,EAAE;YACnC,IAAM,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;YAC/D,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAChD,IAAI,CAAC,4BAAU,CAAC,iBAAiB,CAAC,GAAG,CAAC,EAAE;gBACpC,IAAM,QAAQ,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC;gBAClC,IAAI,QAAQ,EAAE;oBACV,IAAM,QAAQ,GAAG,QAAQ,CAAC,SAAS,CAAC;oBACpC,QAAQ,CAAC,SAAS,GAAG,KAAK,CAAC;oBAC3B,IAAI,CAAC,4BAAU,CAAC,iBAAiB,CAAC,QAAQ,CAAC,IAAI,QAAQ,KAAK,KAAK,EAAE;wBAC/D,OAAO,IAAI,CAAC,uBAAuB,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC;qBAC9D;iBACJ;qBAAM;oBACH,WAAW,CAAC,GAAG,CAAC,GAAG,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC;oBACxC,IAAI,gBAAgB,IAAI,gBAAgB,CAAC,GAAG,CAAC,EAAE;wBAC3C,OAAO,IAAI,CAAC,uBAAuB,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC;qBAC9D;iBACJ;aACJ;iBAAM;gBACH,GAAG,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC;gBACzB,IAAI,WAAW,CAAC,GAAG,CAAC,EAAE;oBAClB,oCAAoC;oBACpC,qDAAqD;oBACrD,6EAA6E;oBAC7E,GAAG,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;iBACzC;gBACD,WAAW,CAAC,GAAG,CAAC,GAAG,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC;aAC3C;YACD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;YACvB,IAAI,CAAC,uBAAuB,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,GAAG,KAAA,EAAE,IAAI,MAAA,EAAE,CAAC;SACpE;QACD,IAAI,CAAC,4BAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE;YAC5D,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;SACzC;QACD,IAAM,SAAS,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC;QACnC,IAAI,SAAS,IAAI,SAAS,CAAC,SAAS,KAAK,KAAK,EAAE;YAC5C,0BAA0B;YAC1B,OAAO,CAAC,IAAI,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC,CAAC,qBAAqB;SAC9E;QACD,OAAO,GAAG,CAAC;IACf,CAAC;IAED,mGAAmG;IAC5F,6CAAmB,GAA1B,UAA2B,eAAiC;QAA5D,iBA0FC;QAzFG,IAAM,WAAW,GAAG,eAAe,CAAC,WAAW,CAAC;QAChD,IAAM,QAAQ,GAAG,eAAe,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;QAC/C,IAAM,eAAe,GAA8B,EAAE,CAAC;QACtD,IAAM,cAAc,GAAgB,EAAE,CAAC;QACvC,IAAM,gBAAgB,GAA8B,EAAE,CAAC;QAEvD,iFAAiF;QACjF,mDAAmD;QACnD,IAAI,IAAI,CAAC,sBAAsB,IAAI,IAAI,CAAC,YAAY,EAAE;YAClD,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;SAChC;QAED,yEAAyE;QACzE,KAAK,IAAM,GAAG,IAAI,IAAI,CAAC,YAAY,EAAE;YACjC,IAAI,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;gBACvC,IAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC;gBAC/C,IAAI,CAAC,4BAAU,CAAC,iBAAiB,CAAC,KAAK,CAAC,EAAE;oBACtC,IAAI,KAAK,IAAI,QAAQ,EAAE;wBACnB,IAAM,QAAQ,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC;wBACpC,eAAe,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;qBACjC;iBACJ;aACJ;SACJ;QAED,4BAA4B;QAC5B,IAAM,kBAAkB,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;QACrE,IAAM,uBAAuB,GAAG,kBAAkB,CAAC,MAAM,CAAC;QAC1D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,uBAAuB,EAAE,CAAC,EAAE,EAAE;YAC9C,IAAM,GAAG,GAAG,kBAAkB,CAAC,CAAC,CAAC,CAAC;YAClC,IAAM,YAAY,GAAG,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,CAAC;YACvD,IAAI,YAAY,EAAE;gBACd,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE;oBACvB,IAAI,CAAC,IAAI,CAAC,sBAAsB,IAAI,IAAI,CAAC,mBAAmB,EAAE;wBAC1D,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,YAAY,CAAC,IAAI,EAAE,YAAY,CAAC,GAAG,CAAC,CAAC;qBAC5E;oBACD,OAAO,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,CAAC;oBAEzC,IAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;oBACtD,IAAM,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC;oBAC9D,IAAI,CAAC,4BAAU,CAAC,iBAAiB,CAAC,SAAS,CAAC,IAAI,SAAS,IAAI,QAAQ,IAAI,IAAI,CAAC,cAAc,EAAE;wBAC1F,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;qBAC/C;iBACJ;qBAAM;oBACH,gBAAgB,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;iBAC5C;aACJ;SACJ;QACD,IAAM,eAAe,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC;YAC7D,IAAM,SAAS,GAAG,KAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACvC,IAAM,UAAU,GAAG,KAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACxC,IAAI,SAAS,IAAI,SAAS,CAAC,SAAS,IAAI,UAAU,IAAI,UAAU,CAAC,SAAS,EAAE;gBACxE,OAAO,SAAS,CAAC,SAAS,GAAG,UAAU,CAAC,SAAS,CAAC;aACrD;YACD,OAAO,CAAC,CAAC;QACb,CAAC,CAAC,CAAC;QACH,IAAM,iBAAiB,GAAG,eAAe,CAAC,MAAM,CAAC;QACjD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,iBAAiB,EAAE,CAAC,EAAE,EAAE;YACxC,IAAM,GAAG,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;YAC/B,IAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC;YAC/C,IAAI,CAAC,4BAAU,CAAC,iBAAiB,CAAC,KAAK,CAAC,EAAE;gBACtC,IAAI,KAAK,IAAI,QAAQ,EAAE;oBACnB,IAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,WAAW,EAAE,cAAc,EAAE,gBAAgB,CAAC,CAAC;oBACxF,IAAM,YAAY,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC;oBAC5C,IAAI,CAAC,YAAY,EAAE;wBACf,cAAc,CAAC,MAAM,CAAC,GAAG,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC;qBACjD;yBAAM,IAAI,YAAY,CAAC,SAAS,KAAK,KAAK,EAAE;wBACzC,IAAM,MAAM,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;wBAC/C,cAAc,CAAC,MAAM,CAAC,GAAG,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC;wBAC9C,IAAI,CAAC,uBAAuB,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,GAAG;4BAC/C,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,eAAe,CAAC,qBAAqB,CAAC,KAAK,CAAC;yBACvE,CAAC;qBACL;iBACJ;aACJ;YACD,OAAO,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;SACjC;QAED,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC;QAEjD,KAAK,IAAM,GAAG,IAAI,IAAI,CAAC,YAAY,EAAE;YACjC,IAAI,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;gBACvC,IAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC;gBAC/C,IAAI,CAAC,4BAAU,CAAC,iBAAiB,CAAC,KAAK,CAAC,IAAI,4BAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE;oBACnG,IAAM,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;oBAC/D,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;iBAClD;aACJ;SACJ;IACL,CAAC;IAEO,kDAAwB,GAAhC;QACI,OAAO,GAAG,GAAG,IAAI,CAAC,SAAS,EAAE,GAAG,QAAQ,CAAC;IAC7C,CAAC;IAEO,oDAA0B,GAAlC;QACI,IAAI,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,OAAO,EAAE;YACrF,IAAI,CAAC,mBAAmB,CAAC,oBAAoB,GAAG,IAAI,CAAC,sBAAsB,CAAC;YAC5E,IAAI,IAAI,CAAC,qBAAqB,EAAE;gBAC5B,IAAI,CAAC,mBAAmB,CAAC,oBAAoB,GAAG,IAAI,CAAC,sBAAsB,CAAC;aAC/E;YACD,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;gBAC7F,IAAI,CAAC,cAAc,CAAC,mBAAmB,EAAE,CAAC,KAAK,CAAC,CAAC;gBACjD,IAAI,CAAC,cAAc,CAAC,mBAAmB,EAAE,CAAC,MAAM,CAAC,CAAC;YACtD,IAAI,CAAC,mBAAmB,CAAC,aAAa,CAAC;gBACnC,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,MAAM;gBAC/B,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,KAAK;aAChC,EAAE,yBAAO,CAAC,KAAK,CAAU,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC,CAAC;SAChE;aAAM;YACH,MAAM,IAAI,qBAAW,CAAC,oCAA0B,CAAC,uBAAuB,CAAC,CAAC;SAC7E;IACL,CAAC;IAgCD,+DAA+D;IACvD,4CAAkB,GAA1B,UAA2B,WAAqB;QAC5C,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QACxB,IAAM,KAAK,GAAG,WAAW,CAAC,MAAM,CAAC;QACjC,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,IAAI,qBAAqB,GAAG,KAAK,CAAC;QAClC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;YAC5B,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;YACvB,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAChC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YAC1B,qBAAqB,GAAG,IAAI,CAAC,UAAU,CAAC;SAC3C;QACD,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QACxB,OAAO,qBAAqB,CAAC;IACjC,CAAC;IACL,sBAAC;AAAD,CAAC,AAjYD,IAiYC"}