"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
var React = require("react");
var BaseScrollComponent = /** @class */ (function (_super) {
    __extends(BaseScrollComponent, _super);
    function BaseScrollComponent() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    //Override and return node handle to your custom scrollview. Useful if you need to use Animated Events.
    BaseScrollComponent.prototype.getScrollableNode = function () {
        return null;
    };
    //Override and return ref to your custom scrollview. Useful if you need to use Animated Events on the new architecture.
    BaseScrollComponent.prototype.getNativeScrollRef = function () {
        return null;
    };
    return BaseScrollComponent;
}(React.Component));
exports.default = BaseScrollComponent;
//# sourceMappingURL=BaseScrollComponent.js.map