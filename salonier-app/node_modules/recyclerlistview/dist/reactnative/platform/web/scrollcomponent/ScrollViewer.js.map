{"version": 3, "file": "ScrollViewer.js", "sourceRoot": "", "sources": ["../../../../../src/platform/web/scrollcomponent/ScrollViewer.tsx"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,6BAA+B;AAC/B,+EAAmH;AACnH,0CAA6C;AAC7C,iEAAgE;AAEhE;;;GAGG;AACH;IAA0C,gCAAc;IAAxD;QAAA,qEAkMC;QA1LW,6BAAuB,GAAG,QAAQ,CAAC,UAAC,UAAsB;YAC9D,UAAU,EAAE,CAAC;QACjB,CAAC,EAAE,IAAI,CAAC,CAAC;QAED,iBAAW,GAA0B,IAAI,CAAC;QAC1C,kBAAY,GAAY,KAAK,CAAC;QAC9B,4BAAsB,GAAiC,IAAI,CAAC;QAqD5D,gBAAU,GAAG,UAAC,GAA0B;YAC5C,KAAI,CAAC,WAAW,GAAG,GAAG,CAAC;YACvB,IAAI,GAAG,EAAE;gBACL,KAAI,CAAC,sBAAsB,GAAG,IAAI,6CAAqB,CAAC,GAAG,CAAC,CAAC;aAChE;iBAAM;gBACH,KAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;aACtC;QACL,CAAC,CAAA;QAEO,wBAAkB,GAAG;YACzB,IAAI,CAAC,KAAI,CAAC,KAAK,CAAC,eAAe,EAAE;gBAC7B,IAAI,KAAI,CAAC,WAAW,EAAE;oBAClB,IAAI,KAAI,CAAC,KAAK,CAAC,UAAU,EAAE;wBACvB,OAAO,KAAI,CAAC,WAAW,CAAC,UAAU,CAAC;qBACtC;yBAAM;wBACH,OAAO,KAAI,CAAC,WAAW,CAAC,SAAS,CAAC;qBACrC;iBACJ;gBACD,OAAO,CAAC,CAAC;aACZ;iBAAM;gBACH,IAAI,KAAI,CAAC,KAAK,CAAC,UAAU,EAAE;oBACvB,OAAO,MAAM,CAAC,OAAO,CAAC;iBACzB;qBAAM;oBACH,OAAO,MAAM,CAAC,OAAO,CAAC;iBACzB;aACJ;QACL,CAAC,CAAA;QAEO,wBAAkB,GAAG,UAAC,MAAc;YACxC,IAAI,CAAC,KAAI,CAAC,KAAK,CAAC,eAAe,EAAE;gBAC7B,IAAI,KAAI,CAAC,WAAW,EAAE;oBAClB,IAAI,KAAI,CAAC,KAAK,CAAC,UAAU,EAAE;wBACvB,KAAI,CAAC,WAAW,CAAC,UAAU,GAAG,MAAM,CAAC;qBACxC;yBAAM;wBACH,KAAI,CAAC,WAAW,CAAC,SAAS,GAAG,MAAM,CAAC;qBACvC;iBACJ;aACJ;iBAAM;gBACH,IAAI,KAAI,CAAC,KAAK,CAAC,UAAU,EAAE;oBACvB,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;iBAC9B;qBAAM;oBACH,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;iBAC9B;aACJ;QACL,CAAC,CAAA;QAEO,kBAAY,GAAG;YACnB,IAAI,KAAI,CAAC,WAAW,EAAE;gBAClB,KAAI,CAAC,WAAW,CAAC,KAAK,CAAC,aAAa,GAAG,MAAM,CAAC;aACjD;YACD,KAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC9B,CAAC,CAAA;QAEO,2BAAqB,GAAG;YAC5B,IAAI,CAAC,KAAI,CAAC,YAAY,EAAE;gBACpB,IAAI,KAAI,CAAC,WAAW,EAAE;oBAClB,KAAI,CAAC,WAAW,CAAC,KAAK,CAAC,aAAa,GAAG,MAAM,CAAC;iBACjD;gBACD,KAAI,CAAC,YAAY,GAAG,IAAI,CAAC;aAC5B;YACD,KAAI,CAAC,uBAAuB,CAAC,KAAI,CAAC,YAAY,CAAC,CAAC;QACpD,CAAC,CAAA;QAoCO,qBAAe,GAAG;YACtB,IAAI,KAAI,CAAC,KAAK,CAAC,aAAa,IAAI,KAAI,CAAC,KAAK,CAAC,eAAe,EAAE;gBACxD,KAAI,CAAC,KAAK,CAAC,aAAa,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,WAAW,EAAE,KAAK,EAAE,MAAM,CAAC,UAAU,EAAE,CAAC,CAAC;aACtF;QACL,CAAC,CAAA;QAEO,qBAAe,GAAG;YACtB,IAAI,KAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;gBACrB,IAAI,KAAI,CAAC,sBAAsB,EAAE;oBAC7B,KAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAI,CAAC,sBAAsB,CAAC,WAAW,CAAC,CAAC;iBAChE;aACJ;QACL,CAAC,CAAA;QAEO,eAAS,GAAG;YAChB,IAAI,KAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;gBACrB,IAAI,KAAI,CAAC,sBAAsB,EAAE;oBAC7B,KAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC;iBAC7D;aACJ;QACL,CAAC,CAAA;;IAUL,CAAC;IAnLU,wCAAiB,GAAxB;QACI,IAAI,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE;YAC1B,IAAI,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE;gBAC5B,IAAI,CAAC,6BAA6B,EAAE,CAAC;gBACrC,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,WAAW,EAAE,KAAK,EAAE,MAAM,CAAC,UAAU,EAAE,CAAC,CAAC;aACtF;iBAAM,IAAI,IAAI,CAAC,WAAW,EAAE;gBACzB,IAAI,CAAC,0BAA0B,EAAE,CAAC;gBAClC,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,YAAY,EAAE,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC,CAAC;aAC5G;SACJ;IACL,CAAC;IAEM,2CAAoB,GAA3B;QACI,MAAM,CAAC,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;QAC3D,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,WAAW,CAAC,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;SAClE;QACD,MAAM,CAAC,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;IAC/D,CAAC;IAEM,+BAAQ,GAAf,UAAgB,WAAwD;QACpE,IAAI,WAAW,CAAC,QAAQ,EAAE;YACtB,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;SACjF;aAAM;YACH,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;SAClF;IACL,CAAC;IAEM,6BAAM,GAAb;QACI,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,eAAe;YAC9B,CAAC,CAAC,6BACE,GAAG,EAAE,IAAI,CAAC,UAAU,EACpB,KAAK,aACD,uBAAuB,EAAE,OAAO,EAChC,MAAM,EAAE,MAAM,EACd,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,EACtD,SAAS,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,EACvD,KAAK,EAAE,MAAM,IACV,IAAI,CAAC,KAAK,CAAC,KAAK;gBAGvB,6BAAK,KAAK,EAAE,EAAE,QAAQ,EAAE,UAAU,EAAE,IAC/B,IAAI,CAAC,KAAK,CAAC,QAAQ,CAClB,CACJ;YACN,CAAC,CAAC,6BACE,GAAG,EAAE,IAAI,CAAC,UAAU,EACpB,KAAK,aAAI,QAAQ,EAAE,UAAU,IAAK,IAAI,CAAC,KAAK,CAAC,KAAK,KACjD,IAAI,CAAC,KAAK,CAAC,QAAQ,CAClB,CAAC;IACf,CAAC;IAiEO,wCAAiB,GAAzB,UAA0B,MAAc;QAAxC,iBAmBC;QAlBG,IAAI,KAAK,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QACtC,IAAI,MAAM,GAAG,KAAK,EAAE;YAChB,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,GAAG,EAAE,KAAK,CAAC,CAAC;SACzC;aAAM;YACH,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,GAAG,EAAE,KAAK,CAAC,CAAC;SACzC;QACD,IAAM,MAAM,GAAG,MAAM,GAAG,KAAK,CAAC;QAC9B,IAAM,SAAS,GAAG,EAAE,CAAC;QACrB,IAAM,QAAQ,GAAG,GAAG,CAAC;QACrB,IAAM,aAAa,GAAG,UAAC,WAAmB;YACtC,WAAW,IAAI,SAAS,CAAC;YACzB,IAAM,QAAQ,GAAG,KAAI,CAAC,UAAU,CAAC,WAAW,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;YACvE,KAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;YAClC,IAAI,WAAW,GAAG,QAAQ,EAAE;gBACxB,MAAM,CAAC,UAAU,CAAC,cAAM,OAAA,aAAa,CAAC,WAAW,CAAC,EAA1B,CAA0B,EAAE,SAAS,CAAC,CAAC;aAClE;QACL,CAAC,CAAC;QACF,aAAa,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC;IAEO,iDAA0B,GAAlC;QACI,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;SAC/D;IACL,CAAC;IAEO,oDAA6B,GAArC;QACI,MAAM,CAAC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;QACxD,IAAI,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE;YAC1B,MAAM,CAAC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;SAC3D;IACL,CAAC;IAwBO,iCAAU,GAAlB,UAAmB,WAAmB,EAAE,KAAa,EAAE,MAAc,EAAE,QAAgB;QACnF,WAAW,IAAI,QAAQ,GAAG,CAAC,CAAC;QAC5B,IAAI,WAAW,GAAG,CAAC,EAAE;YACjB,OAAO,MAAM,GAAG,CAAC,GAAG,WAAW,GAAG,WAAW,GAAG,KAAK,CAAC;SACzD;QACD,WAAW,IAAI,CAAC,CAAC;QACjB,OAAO,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,WAAW,GAAG,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;IACzE,CAAC;IAhMa,yBAAY,GAAG;QACzB,aAAa,EAAE,KAAK;QACpB,UAAU,EAAE,KAAK;QACjB,KAAK,EAAE,IAAI;QACX,eAAe,EAAE,KAAK;KACzB,CAAC;IA4LN,mBAAC;CAAA,AAlMD,CAA0C,wBAAc,GAkMvD;kBAlMoB,YAAY"}