{"version": 3, "file": "ViewRenderer.js", "sourceRoot": "", "sources": ["../../../../../src/platform/reactnative/viewrenderer/ViewRenderer.tsx"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,6BAA+B;AAC/B,6CAAuE;AAGvE,gFAAkG;AAElG;;;;;GAKG;AACH;IAA0C,gCAAqB;IAA/D;QAAA,qEAmGC;QAlGW,UAAI,GAAc,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;QAC1C,cAAQ,GAAiE,IAAI,CAAC;QAwD9E,aAAO,GAAG,UAAC,IAAkE;YACjF,KAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACzB,CAAC,CAAA;QAEO,eAAS,GAAG,UAAC,KAAwB;YACzC,qFAAqF;YACrF,IAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAI,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAClE,IAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAI,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAClE,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC;gBACtB,CAAC,KAAI,CAAC,KAAK,CAAC,MAAM,KAAK,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM;oBAClD,KAAI,CAAC,KAAK,CAAC,KAAK,KAAK,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;gBAC1D,KAAI,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC;gBACnD,KAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC;gBACjD,IAAI,KAAI,CAAC,KAAK,CAAC,aAAa,EAAE;oBAC1B,KAAI,CAAC,KAAK,CAAC,aAAa,CAAC,KAAI,CAAC,IAAI,EAAE,KAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;iBACzD;aACJ;YAED,IAAI,KAAI,CAAC,KAAK,CAAC,YAAY,EAAE;gBACzB,KAAI,CAAC,KAAK,CAAC,YAAY,CAAC,KAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;aAC7C;QACL,CAAC,CAAA;QAEO,mCAA6B,GAAG;YACpC,yGAAyG;YACzG,IAAI,CAAC,KAAI,CAAC,KAAK,CAAC,8BAA8B,EAAE;gBAC5C,OAAO;aACV;YACD,IAAM,MAAM,gBAAO,KAAI,CAAC,IAAI,CAAC,CAAC;YAC9B,UAAU,CAAC;gBACP,KAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;YAClC,CAAC,EAAE,EAAE,CAAC,CAAC;QACX,CAAC,CAAA;QAEO,sBAAgB,GAAG,UAAC,GAAc;YACtC,IAAI,GAAG,CAAC,KAAK,KAAK,KAAI,CAAC,IAAI,CAAC,KAAK,IAAI,GAAG,CAAC,MAAM,KAAK,KAAI,CAAC,IAAI,CAAC,MAAM,EAAE;gBAClE,IAAI,KAAI,CAAC,iBAAiB,IAAI,KAAI,CAAC,KAAK,CAAC,aAAa,EAAE;oBACpD,KAAI,CAAC,KAAK,CAAC,aAAa,CAAC,KAAI,CAAC,IAAI,EAAE,KAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;iBACzD;aACJ;QACL,CAAC,CAAA;;IACL,CAAC;IA/FU,mCAAY,GAAnB;QACI,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,8BAA8B;YACrD,CAAC,CAAC;gBACE,GAAG,EAAE,IAAI,CAAC,OAAO;gBACjB,QAAQ,EAAE,IAAI,CAAC,SAAS;gBACxB,KAAK,sBACH,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,EACzD,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAClB,QAAQ,EAAE,UAAU,EACpB,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,IACd,IAAI,CAAC,KAAK,CAAC,cAAc,GACzB,IAAI,CAAC,sBAAsB,CAC/B;aACF;YACH,CAAC,CAAC;gBACE,GAAG,EAAE,IAAI,CAAC,OAAO;gBACjB,KAAK,sBACH,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAClB,QAAQ,EAAE,UAAU,EACpB,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EACjB,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,EACzB,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,IACpB,IAAI,CAAC,KAAK,CAAC,cAAc,GACzB,IAAI,CAAC,sBAAsB,CAC/B;aACF,CAAC;QACN,OAAO,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,WAAW,EAAE,CAAgB,CAAC;IAC3F,CAAC;IAEM,yCAAkB,GAAzB;QACI,iBAAM,kBAAkB,WAAE,CAAC;QAC3B,IAAI,IAAI,CAAC,KAAK,CAAC,cAAc,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACrD,IAAI,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,gBAAgB,EAAE,KAAK,IAAI,CAAC,iBAAiB,EAAE;gBACzE,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,gBAAgB,EAAE,CAAC;gBACtE,IAAI,CAAC,6BAA6B,EAAE,CAAC;aACxC;SACJ;IACL,CAAC;IAEM,wCAAiB,GAAxB;QACI,iBAAM,iBAAiB,WAAE,CAAC;QAC1B,IAAI,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE;YAC3B,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,gBAAgB,EAAE,CAAC;SACzE;IACL,CAAC;IAES,6BAAM,GAAhB;QACI,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAEO,2CAAoB,GAA5B,UAA6B,KAAa,EAAE,WAAmC,EAAE,QAAyB;QACtG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,mBAAmB,IAAI,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,KAAK,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,oBAAC,mBAAI,eAAK,KAAK,GAAG,QAAQ,CAAQ,CAAC,CAAC;IACpJ,CAAC;IA2CL,mBAAC;AAAD,CAAC,AAnGD,CAA0C,0BAAgB,GAmGzD"}