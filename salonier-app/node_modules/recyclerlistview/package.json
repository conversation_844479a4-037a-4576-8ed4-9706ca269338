{"name": "recyclerlistview", "version": "4.2.3", "description": "The listview that you need and deserve. It was built for performance, uses cell recycling to achieve smooth scrolling.", "main": "dist/reactnative/index.js", "types": "dist/reactnative/index.d.ts", "scripts": {"build": "sh scripts/build.sh", "clean": "sh scripts/clean.sh", "release": "sh scripts/publish-latest.sh", "release-beta": "sh scripts/publish-beta.sh", "release-local": "sh scripts/publish-local.sh"}, "keywords": ["react-native", "recyclerview", "listview", "flatlist-alternative", "flatlist", "60fps", "cross-platform", "web", "react-native-web", "performance"], "repository": {"type": "git", "url": "https://github.com/Flipkart/recyclerlistview"}, "author": "naqvitalha <<EMAIL>>", "license": "Apache-2.0", "bugs": {"url": "https://github.com/Flipkart/recyclerlistview/issues"}, "homepage": "https://github.com/Flipkart/recyclerlistview", "dependencies": {"lodash.debounce": "4.0.8", "prop-types": "15.8.1", "ts-object-utils": "0.0.5"}, "peerDependencies": {"react": ">= 15.2.1", "react-native": ">= 0.30.0"}, "devDependencies": {"@types/lodash.debounce": "4.0.8", "@types/prop-types": "15.5.2", "@types/react": "16.4.7", "@types/react-native": "0.49.5", "@types/resize-observer-browser": "0.1.7", "file-directives": "1.4.6", "tslint": "5.11.0", "typescript": "4.9.5"}}