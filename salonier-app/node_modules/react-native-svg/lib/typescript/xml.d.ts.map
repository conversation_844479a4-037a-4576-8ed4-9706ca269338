{"version": 3, "file": "xml.d.ts", "sourceRoot": "", "sources": ["../../src/xml.tsx"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,aAAa,EAAE,cAAc,EAAE,GAAG,EAAE,MAAM,OAAO,CAAC;AAEhE,OAAO,EAAE,SAAS,EAAgC,MAAM,OAAO,CAAC;AAEhE,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,gBAAgB,CAAC;AAC/C,OAAO,EAAE,IAAI,EAAE,MAAM,WAAW,CAAC;AAMjC,KAAK,GAAG,GAAG,aAAa,CAAC,cAAc,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,MAAM,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC;AAC3E,MAAM,WAAW,GAAG;IAClB,GAAG,EAAE,MAAM,CAAC;IACZ,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,QAAQ,CAAC,EAAE,GAAG,CAAC,MAAM,EAAE,OAAO,GAAG,SAAS,CAAC,CAAC;IAC5C,MAAM,EAAE,GAAG,GAAG,IAAI,CAAC;IACnB,QAAQ,EAAE,CAAC,GAAG,GAAG,MAAM,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,OAAO,GAAG,MAAM,CAAC,EAAE,CAAC;IACtD,KAAK,EAAE;QACL,CAAC,IAAI,EAAE,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,SAAS,CAAC;KAC7C,CAAC;IACF,GAAG,EAAE,GAAG,CAAC;CACV;AAED,MAAM,WAAW,MAAO,SAAQ,GAAG;IACjC,QAAQ,EAAE,CAAC,MAAM,GAAG,MAAM,CAAC,EAAE,CAAC;IAC9B,MAAM,EAAE,MAAM,GAAG,IAAI,CAAC;CACvB;AAED,MAAM,WAAW,MAAO,SAAQ,GAAG;IACjC,QAAQ,EAAE,CAAC,GAAG,CAAC,OAAO,GAAG,MAAM,CAAC,EAAE,CAAC;CACpC;AAED,MAAM,MAAM,eAAe,GAAG;IAC5B,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,KAAK,IAAI,CAAC;IACjC,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,MAAM,CAAC,EAAE,MAAM,IAAI,CAAC;IACpB,QAAQ,CAAC,EAAE,GAAG,CAAC,OAAO,CAAC;CACxB,CAAC;AAEF,MAAM,MAAM,QAAQ,GAAG,QAAQ,GAAG;IAAE,GAAG,EAAE,MAAM,GAAG,IAAI,CAAA;CAAE,GAAG,eAAe,CAAC;AAC3E,MAAM,MAAM,QAAQ,GAAG;IAAE,GAAG,EAAE,MAAM,GAAG,IAAI,CAAA;CAAE,CAAC;AAE9C,MAAM,MAAM,QAAQ,GAAG,QAAQ,GAAG;IAAE,GAAG,EAAE,MAAM,GAAG,IAAI,CAAA;CAAE,GAAG,eAAe,CAAC;AAC3E,MAAM,MAAM,QAAQ,GAAG;IAAE,GAAG,EAAE,MAAM,GAAG,IAAI,CAAA;CAAE,CAAC;AAE9C,MAAM,MAAM,QAAQ,GAAG,QAAQ,GAAG;IAAE,GAAG,EAAE,MAAM,GAAG,IAAI,CAAA;CAAE,GAAG,eAAe,CAAC;AAE3E,wBAAgB,MAAM,CAAC,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,QAAQ,sBAajD;AAID,wBAAgB,MAAM,CAAC,KAAK,EAAE,QAAQ,sBAarC;AAED,wBAAgB,MAAM,CAAC,KAAK,EAAE,QAAQ,sBAuBrC;AAID,qBAAa,UAAW,SAAQ,SAAS,CAAC,QAAQ,EAAE,QAAQ,CAAC;IAC3D,KAAK;;MAAiB;IACtB,iBAAiB;IAIjB,kBAAkB,CAAC,SAAS,EAAE;QAAE,GAAG,EAAE,MAAM,GAAG,IAAI,CAAA;KAAE;IAOpD,KAAK,CAAC,GAAG,EAAE,MAAM,GAAG,IAAI;IAaxB,MAAM;CAOP;AAED,qBAAa,UAAW,SAAQ,SAAS,CAAC,QAAQ,EAAE,QAAQ,CAAC;IAC3D,KAAK;;MAAiB;IACtB,iBAAiB;IAIjB,kBAAkB,CAAC,SAAS,EAAE;QAAE,GAAG,EAAE,MAAM,GAAG,IAAI,CAAA;KAAE;IAO9C,KAAK,CAAC,GAAG,EAAE,MAAM,GAAG,IAAI;IAQ9B,MAAM;CAOP;AAID,eAAO,MAAM,SAAS,WAAY,MAAM,WACG,CAAC;AAE5C,MAAM,MAAM,MAAM,GAAG;IAAE,CAAC,QAAQ,EAAE,MAAM,GAAG,MAAM,CAAA;CAAE,CAAC;AAEpD,wBAAgB,QAAQ,CAAC,MAAM,EAAE,MAAM,GAAG,MAAM,CAc/C;AAED,wBAAgB,UAAU,CACxB,KAAK,EAAE,GAAG,GAAG,MAAM,EACnB,KAAK,EAAE,MAAM,GACZ,GAAG,CAAC,OAAO,GAAG,MAAM,CAetB;AA2CD,MAAM,MAAM,UAAU,GAAG,CAAC,GAAG,EAAE,MAAM,KAAK,MAAM,CAAC;AAEjD,wBAAgB,KAAK,CAAC,MAAM,EAAE,MAAM,EAAE,UAAU,CAAC,EAAE,UAAU,GAAG,MAAM,GAAG,IAAI,CA0R5E;AACD,OAAO,EAAE,IAAI,EAAE,CAAC"}