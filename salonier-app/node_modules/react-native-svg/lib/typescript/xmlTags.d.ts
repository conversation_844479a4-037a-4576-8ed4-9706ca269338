import { Circle, <PERSON>lipPath, Defs, El<PERSON>se, FeBlend, FeColorMatrix, FeComponentTransfer, FeComposite, FeConvolveMatrix, FeDiffuseLighting, FeDisplacementMap, FeDistantLight, FeDropShadow, FeFlood, FeGaussianBlur, FeImage, FeMerge, FeMergeNode, FeMorphology, FeOffset, FePointLight, FeSpecularLighting, FeSpotLight, FeTile, FeTurbulence, Filter, ForeignObject, G, Image, Line, LinearGradient, Marker, Mask, Path, Pattern, Polygon, Polyline, RadialGradient, Rect, Stop, Svg, Symbol, Text, TextPath, TSpan, Use } from './elements';
export declare const tags: {
    readonly circle: typeof Circle;
    readonly clipPath: typeof ClipPath;
    readonly defs: typeof Defs;
    readonly ellipse: typeof Ellipse;
    readonly filter: typeof Filter;
    readonly feBlend: typeof FeBlend;
    readonly feColorMatrix: typeof FeColorMatrix;
    readonly feComponentTransfer: typeof FeComponentTransfer;
    readonly feComposite: typeof FeComposite;
    readonly feConvolveMatrix: typeof FeConvolveMatrix;
    readonly feDiffuseLighting: typeof FeDiffuseLighting;
    readonly feDisplacementMap: typeof FeDisplacementMap;
    readonly feDistantLight: typeof FeDistantLight;
    readonly feDropShadow: typeof FeDropShadow;
    readonly feFlood: typeof FeFlood;
    readonly feGaussianBlur: typeof FeGaussianBlur;
    readonly feImage: typeof FeImage;
    readonly feMerge: typeof FeMerge;
    readonly feMergeNode: typeof FeMergeNode;
    readonly feMorphology: typeof FeMorphology;
    readonly feOffset: typeof FeOffset;
    readonly fePointLight: typeof FePointLight;
    readonly feSpecularLighting: typeof FeSpecularLighting;
    readonly feSpotLight: typeof FeSpotLight;
    readonly feTile: typeof FeTile;
    readonly feTurbulence: typeof FeTurbulence;
    readonly foreignObject: typeof ForeignObject;
    readonly g: typeof G;
    readonly image: typeof Image;
    readonly line: typeof Line;
    readonly linearGradient: typeof LinearGradient;
    readonly marker: typeof Marker;
    readonly mask: typeof Mask;
    readonly path: typeof Path;
    readonly pattern: typeof Pattern;
    readonly polygon: typeof Polygon;
    readonly polyline: typeof Polyline;
    readonly radialGradient: typeof RadialGradient;
    readonly rect: typeof Rect;
    readonly stop: typeof Stop;
    readonly svg: typeof Svg;
    readonly symbol: typeof Symbol;
    readonly text: typeof Text;
    readonly textPath: typeof TextPath;
    readonly tspan: typeof TSpan;
    readonly use: typeof Use;
};
//# sourceMappingURL=xmlTags.d.ts.map